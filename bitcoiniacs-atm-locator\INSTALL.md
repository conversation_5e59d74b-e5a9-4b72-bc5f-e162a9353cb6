# Installation Guide - Bitcoiniacs ATM Locator

## Quick Start

1. **Upload Plugin**
   - Upload the `bitcoiniacs-atm-locator` folder to `/wp-content/plugins/`
   - Or upload the zip file through WordPress admin

2. **Activate Plugin**
   - Go to Plugins → Installed Plugins
   - Find "Bitcoiniacs ATM Locator" and click "Activate"

3. **Verify Installation**
   - Check that "ATM Locations" appears in the admin menu
   - Visit ATM Locations → All Locations to see seed data

4. **Add to Page**
   - Create a new page or edit existing one
   - Add the shortcode: `[bitcoiniacs_atm_locator]`
   - Or use the Elementor widget if using Elementor

## Detailed Setup

### Step 1: Plugin Installation

#### Method A: WordPress Admin Upload
1. Download the plugin zip file
2. Go to WordPress Admin → Plugins → Add New
3. Click "Upload Plugin"
4. Choose the zip file and click "Install Now"
5. Click "Activate Plugin"

#### Method B: FTP Upload
1. Extract the plugin zip file
2. Upload the `bitcoiniacs-atm-locator` folder to `/wp-content/plugins/`
3. Go to WordPress Admin → Plugins
4. Find "Bitcoiniacs ATM Locator" and click "Activate"

### Step 2: Database Setup

The plugin automatically:
- Creates the `wp_bitcoiniacs_atm_locations` table
- Populates it with seed data (Canadian and international ATM locations)
- Sets up default plugin options

### Step 3: Configuration

1. **Access Admin Panel**
   - Go to ATM Locations in the WordPress admin menu

2. **Review Settings**
   - Go to ATM Locations → Settings
   - Configure default map center and zoom
   - Choose preferred map style

3. **Verify Seed Data**
   - Go to ATM Locations → All Locations
   - You should see pre-populated ATM locations

### Step 4: Frontend Display

#### Using Shortcode
1. Edit any page or post
2. Add the shortcode: `[bitcoiniacs_atm_locator]`
3. Customize with parameters if needed:
   ```
   [bitcoiniacs_atm_locator height="500" province="BC" show_visual_selector="true"]
   ```

#### Using Elementor
1. Edit a page with Elementor
2. Search for "Bitcoin ATM Locator" widget
3. Drag to desired location
4. Configure widget settings in the panel

### Step 5: Customization

#### Adding Your Own Locations
1. Go to ATM Locations → Add New
2. Fill in the form:
   - Location Name (required)
   - Address (required)
   - Province/State (required)
   - Country (required)
   - Coordinates (use "Get Coordinates" button)
   - Services (Buy/Sell options)
   - Additional details

#### Styling Customization
- Frontend styles: `/wp-content/plugins/bitcoiniacs-atm-locator/assets/css/frontend.css`
- Admin styles: `/wp-content/plugins/bitcoiniacs-atm-locator/assets/css/admin.css`

## Verification Checklist

After installation, verify these items work:

- [ ] Admin menu "ATM Locations" appears
- [ ] Seed data is visible in All Locations
- [ ] Shortcode displays map on frontend
- [ ] Map loads with markers
- [ ] Filters work (province, service type)
- [ ] Search functionality works
- [ ] "Find Nearest" button works (requires HTTPS for geolocation)
- [ ] Visual selector tabs work
- [ ] Admin forms save correctly

## Common Issues

### Map Not Loading
- **Cause**: JavaScript errors or network issues
- **Solution**: Check browser console, verify internet connection

### Geolocation Not Working
- **Cause**: HTTP site (geolocation requires HTTPS)
- **Solution**: Use HTTPS or test location features manually

### Coordinates Not Auto-Filling
- **Cause**: Geocoding service unavailable
- **Solution**: Enter coordinates manually or try again later

### Styling Issues
- **Cause**: Theme CSS conflicts
- **Solution**: Add custom CSS to override theme styles

## Uninstallation

To completely remove the plugin:

1. **Deactivate Plugin**
   - Go to Plugins → Installed Plugins
   - Deactivate "Bitcoiniacs ATM Locator"

2. **Delete Plugin**
   - Click "Delete" on the deactivated plugin
   - This will remove all plugin files and database data

**Note**: Uninstalling will permanently delete all ATM location data.

## File Permissions

Ensure proper file permissions:
- Plugin folder: 755
- PHP files: 644
- CSS/JS files: 644

## Server Requirements

- **PHP**: 7.4 or higher
- **WordPress**: 5.0 or higher
- **MySQL**: 5.6 or higher
- **Memory**: 128MB minimum (256MB recommended)

## Support

If you encounter issues:

1. Check the troubleshooting section in README.md
2. Verify all requirements are met
3. Check browser console for JavaScript errors
4. Contact support with specific error messages

## Next Steps

After successful installation:

1. **Customize Settings**: Adjust map defaults in ATM Locations → Settings
2. **Add Locations**: Add your own ATM locations
3. **Style Integration**: Customize CSS to match your theme
4. **Test Features**: Test all functionality including geolocation
5. **User Training**: Train content editors on adding/editing locations
