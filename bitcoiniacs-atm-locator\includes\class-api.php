<?php
/**
 * API functionality for Bitcoiniacs ATM Locator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

class BitcoiniacsATMAPI {
  
  /**
   * Constructor
   */
  public function __construct() {
    add_action('rest_api_init', array($this, 'register_rest_routes'));
  }
  
  /**
   * Register REST API routes
   */
  public function register_rest_routes() {
    register_rest_route('bitcoiniacs-atm/v1', '/locations', array(
      'methods' => 'GET',
      'callback' => array($this, 'get_locations'),
      'permission_callback' => '__return_true',
      'args' => array(
        'province' => array(
          'description' => 'Filter by province',
          'type' => 'string',
          'sanitize_callback' => 'sanitize_text_field'
        ),
        'country' => array(
          'description' => 'Filter by country',
          'type' => 'string',
          'sanitize_callback' => 'sanitize_text_field'
        ),
        'service' => array(
          'description' => 'Filter by service type (buy, sell, both)',
          'type' => 'string',
          'sanitize_callback' => 'sanitize_text_field'
        ),
        'search' => array(
          'description' => 'Search term for location name or address',
          'type' => 'string',
          'sanitize_callback' => 'sanitize_text_field'
        ),
        'lat' => array(
          'description' => 'User latitude for distance calculation',
          'type' => 'number'
        ),
        'lng' => array(
          'description' => 'User longitude for distance calculation',
          'type' => 'number'
        ),
        'radius' => array(
          'description' => 'Search radius in kilometers',
          'type' => 'number',
          'default' => 50
        )
      )
    ));
    
    register_rest_route('bitcoiniacs-atm/v1', '/locations/(?P<id>\d+)', array(
      'methods' => 'GET',
      'callback' => array($this, 'get_location'),
      'permission_callback' => '__return_true',
      'args' => array(
        'id' => array(
          'description' => 'Location ID',
          'type' => 'integer',
          'sanitize_callback' => 'absint'
        )
      )
    ));
    
    register_rest_route('bitcoiniacs-atm/v1', '/provinces', array(
      'methods' => 'GET',
      'callback' => array($this, 'get_provinces'),
      'permission_callback' => '__return_true'
    ));
  }
  
  /**
   * Get locations via REST API
   */
  public function get_locations($request) {
    $filters = array();
    
    if ($request->get_param('province')) {
      $filters['province'] = $request->get_param('province');
    }
    
    if ($request->get_param('country')) {
      $filters['country'] = $request->get_param('country');
    }
    
    if ($request->get_param('service')) {
      $filters['service'] = $request->get_param('service');
    }
    
    if ($request->get_param('search')) {
      $filters['search'] = $request->get_param('search');
    }
    
    $locations = BitcoiniacsATMDatabase::get_all_locations($filters);
    
    $user_lat = $request->get_param('lat');
    $user_lng = $request->get_param('lng');
    $radius = $request->get_param('radius') ?: 50;
    
    $formatted_locations = array();
    foreach ($locations as $location) {
      $location_data = array(
        'id' => intval($location->id),
        'name' => $location->location_name,
        'address' => $location->address,
        'province' => $location->province,
        'country' => $location->country,
        'latitude' => floatval($location->latitude),
        'longitude' => floatval($location->longitude),
        'status' => $location->status,
        'services' => array(
          'buy' => (bool) $location->can_buy,
          'sell' => (bool) $location->can_sell
        ),
        'operating_hours' => $location->operating_hours,
        'phone' => $location->phone,
        'website' => $location->website,
        'notes' => $location->notes,
        'created_at' => $location->created_at,
        'updated_at' => $location->updated_at
      );
      
      // Calculate distance if user location is provided
      if ($user_lat !== null && $user_lng !== null) {
        $distance = $this->calculate_distance(
          $user_lat, $user_lng,
          $location_data['latitude'], $location_data['longitude']
        );
        $location_data['distance_km'] = round($distance, 1);
        
        // Filter by radius if specified
        if ($radius > 0 && $distance > $radius) {
          continue;
        }
      }
      
      $formatted_locations[] = $location_data;
    }
    
    // Sort by distance if user location is provided
    if ($user_lat !== null && $user_lng !== null) {
      usort($formatted_locations, function($a, $b) {
        return $a['distance_km'] <=> $b['distance_km'];
      });
    }
    
    return rest_ensure_response($formatted_locations);
  }
  
  /**
   * Get single location via REST API
   */
  public function get_location($request) {
    $location_id = $request->get_param('id');
    $location = BitcoiniacsATMDatabase::get_location($location_id);
    
    if (!$location) {
      return new WP_Error('location_not_found', 'Location not found', array('status' => 404));
    }
    
    $location_data = array(
      'id' => intval($location->id),
      'name' => $location->location_name,
      'address' => $location->address,
      'province' => $location->province,
      'country' => $location->country,
      'latitude' => floatval($location->latitude),
      'longitude' => floatval($location->longitude),
      'status' => $location->status,
      'services' => array(
        'buy' => (bool) $location->can_buy,
        'sell' => (bool) $location->can_sell
      ),
      'operating_hours' => $location->operating_hours,
      'phone' => $location->phone,
      'website' => $location->website,
      'notes' => $location->notes,
      'created_at' => $location->created_at,
      'updated_at' => $location->updated_at
    );
    
    return rest_ensure_response($location_data);
  }
  
  /**
   * Get provinces via REST API
   */
  public function get_provinces($request) {
    $provinces = BitcoiniacsATMDatabase::get_provinces();
    
    $formatted_provinces = array();
    foreach ($provinces as $province) {
      $formatted_provinces[] = array(
        'province' => $province->province,
        'country' => $province->country
      );
    }
    
    return rest_ensure_response($formatted_provinces);
  }
  
  /**
   * Calculate distance between two points using Haversine formula
   */
  private function calculate_distance($lat1, $lng1, $lat2, $lng2) {
    $earth_radius = 6371; // Earth's radius in kilometers
    
    $lat1_rad = deg2rad($lat1);
    $lng1_rad = deg2rad($lng1);
    $lat2_rad = deg2rad($lat2);
    $lng2_rad = deg2rad($lng2);
    
    $delta_lat = $lat2_rad - $lat1_rad;
    $delta_lng = $lng2_rad - $lng1_rad;
    
    $a = sin($delta_lat / 2) * sin($delta_lat / 2) +
         cos($lat1_rad) * cos($lat2_rad) *
         sin($delta_lng / 2) * sin($delta_lng / 2);
    
    $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
    
    return $earth_radius * $c;
  }
}
