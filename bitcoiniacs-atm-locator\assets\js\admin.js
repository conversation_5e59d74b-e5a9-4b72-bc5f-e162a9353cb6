/**
 * Admin JavaScript for Bitcoiniacs ATM Locator
 */

(function($) {
  'use strict';

  $(document).ready(function() {
    
    // Initialize admin functionality
    BitcoiniacsATMAdmin.init();
    
  });

  // Admin object
  window.BitcoiniacsATMAdmin = {
    
    /**
     * Initialize admin functionality
     */
    init: function() {
      this.setupLocationForm();
      this.setupBulkActions();
      this.setupDeleteButtons();
      this.setupGeocoding();
      this.setupFormValidation();
    },
    
    /**
     * Setup location form handling
     */
    setupLocationForm: function() {
      const self = this;
      
      $('#location-form').on('submit', function(e) {
        e.preventDefault();
        
        if (!self.validateForm()) {
          return false;
        }
        
        const formData = $(this).serialize();
        const submitButton = $(this).find('input[type="submit"]');
        const originalText = submitButton.val();
        
        // Show loading state
        submitButton.val('Saving...').prop('disabled', true);
        
        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: formData + '&action=bitcoiniacs_atm_save_location',
          success: function(response) {
            if (response.success) {
              self.showNotice('success', response.data);
              setTimeout(function() {
                window.location.href = bitcoiniacs_atm_admin.admin_url + 'admin.php?page=bitcoiniacs-atm-locations';
              }, 1500);
            } else {
              self.showNotice('error', response.data);
              submitButton.val(originalText).prop('disabled', false);
            }
          },
          error: function() {
            self.showNotice('error', 'Network error occurred. Please try again.');
            submitButton.val(originalText).prop('disabled', false);
          }
        });
      });
    },
    
    /**
     * Setup bulk actions
     */
    setupBulkActions: function() {
      const self = this;
      
      // Select all checkbox
      $('#cb-select-all-1').on('change', function() {
        $('input[name="locations[]"]').prop('checked', this.checked);
      });
      
      // Individual checkboxes
      $(document).on('change', 'input[name="locations[]"]', function() {
        const totalCheckboxes = $('input[name="locations[]"]').length;
        const checkedCheckboxes = $('input[name="locations[]"]:checked').length;
        
        $('#cb-select-all-1').prop('checked', totalCheckboxes === checkedCheckboxes);
      });
      
      // Bulk action form submission
      $('.bitcoiniacs-bulk-actions form').on('submit', function(e) {
        const action = $(this).find('select[name="action"]').val();
        const selectedItems = $('input[name="locations[]"]:checked').length;
        
        if (action === '-1') {
          e.preventDefault();
          alert('Please select an action.');
          return false;
        }
        
        if (selectedItems === 0) {
          e.preventDefault();
          alert('Please select at least one location.');
          return false;
        }
        
        if (action === 'delete') {
          if (!confirm('Are you sure you want to delete the selected locations? This action cannot be undone.')) {
            e.preventDefault();
            return false;
          }
        }
      });
    },
    
    /**
     * Setup delete buttons
     */
    setupDeleteButtons: function() {
      const self = this;
      
      $(document).on('click', '.button-link-delete', function(e) {
        e.preventDefault();
        
        const locationId = $(this).data('id');
        const locationName = $(this).closest('tr').find('td:first strong a').text();
        
        if (!confirm(`Are you sure you want to delete "${locationName}"? This action cannot be undone.`)) {
          return;
        }
        
        const button = $(this);
        const originalText = button.text();
        
        button.text('Deleting...').prop('disabled', true);
        
        $.ajax({
          url: ajaxurl,
          type: 'POST',
          data: {
            action: 'bitcoiniacs_atm_delete_location',
            id: locationId,
            nonce: bitcoiniacs_atm_admin.nonce
          },
          success: function(response) {
            if (response.success) {
              button.closest('tr').fadeOut(300, function() {
                $(this).remove();
                self.showNotice('success', response.data);
              });
            } else {
              self.showNotice('error', response.data);
              button.text(originalText).prop('disabled', false);
            }
          },
          error: function() {
            self.showNotice('error', 'Network error occurred. Please try again.');
            button.text(originalText).prop('disabled', false);
          }
        });
      });
    },
    
    /**
     * Setup geocoding functionality
     */
    setupGeocoding: function() {
      const self = this;
      
      $('#geocode-btn').on('click', function(e) {
        e.preventDefault();
        
        const address = $('#address').val().trim();
        if (!address) {
          alert('Please enter an address first.');
          return;
        }
        
        const button = $(this);
        const originalText = button.text();
        
        button.text('Getting coordinates...').prop('disabled', true).addClass('geocode-loading');
        
        // Use Nominatim geocoding service
        const geocodeUrl = 'https://nominatim.openstreetmap.org/search?format=json&q=' + encodeURIComponent(address);
        
        $.ajax({
          url: geocodeUrl,
          type: 'GET',
          dataType: 'json',
          success: function(data) {
            if (data && data.length > 0) {
              $('#latitude').val(parseFloat(data[0].lat).toFixed(8));
              $('#longitude').val(parseFloat(data[0].lon).toFixed(8));
              self.showNotice('success', 'Coordinates found and filled in.');
            } else {
              self.showNotice('warning', 'Could not find coordinates for this address. Please enter them manually.');
            }
          },
          error: function() {
            self.showNotice('error', 'Geocoding service unavailable. Please enter coordinates manually.');
          },
          complete: function() {
            button.text(originalText).prop('disabled', false).removeClass('geocode-loading');
          }
        });
      });
    },
    
    /**
     * Setup form validation
     */
    setupFormValidation: function() {
      const self = this;
      
      // Real-time validation
      $('#location-form input[required], #location-form textarea[required], #location-form select[required]').on('blur', function() {
        self.validateField($(this));
      });
      
      // Coordinate validation
      $('#latitude, #longitude').on('input', function() {
        const value = parseFloat($(this).val());
        const field = $(this).attr('id');
        
        if (isNaN(value)) {
          self.showFieldError($(this), 'Please enter a valid number.');
        } else if (field === 'latitude' && (value < -90 || value > 90)) {
          self.showFieldError($(this), 'Latitude must be between -90 and 90.');
        } else if (field === 'longitude' && (value < -180 || value > 180)) {
          self.showFieldError($(this), 'Longitude must be between -180 and 180.');
        } else {
          self.clearFieldError($(this));
        }
      });
    },
    
    /**
     * Validate entire form
     */
    validateForm: function() {
      const self = this;
      let isValid = true;
      
      // Check required fields
      $('#location-form input[required], #location-form textarea[required], #location-form select[required]').each(function() {
        if (!self.validateField($(this))) {
          isValid = false;
        }
      });
      
      // Validate coordinates
      const lat = parseFloat($('#latitude').val());
      const lng = parseFloat($('#longitude').val());
      
      if (isNaN(lat) || lat < -90 || lat > 90) {
        self.showFieldError($('#latitude'), 'Please enter a valid latitude between -90 and 90.');
        isValid = false;
      }
      
      if (isNaN(lng) || lng < -180 || lng > 180) {
        self.showFieldError($('#longitude'), 'Please enter a valid longitude between -180 and 180.');
        isValid = false;
      }
      
      return isValid;
    },
    
    /**
     * Validate individual field
     */
    validateField: function($field) {
      const value = $field.val().trim();
      
      if ($field.prop('required') && !value) {
        this.showFieldError($field, 'This field is required.');
        return false;
      }
      
      this.clearFieldError($field);
      return true;
    },
    
    /**
     * Show field error
     */
    showFieldError: function($field, message) {
      $field.addClass('bitcoiniacs-form-error');
      
      let $errorMsg = $field.siblings('.bitcoiniacs-error-message');
      if ($errorMsg.length === 0) {
        $errorMsg = $('<span class="bitcoiniacs-error-message"></span>');
        $field.after($errorMsg);
      }
      
      $errorMsg.text(message);
    },
    
    /**
     * Clear field error
     */
    clearFieldError: function($field) {
      $field.removeClass('bitcoiniacs-form-error');
      $field.siblings('.bitcoiniacs-error-message').remove();
    },
    
    /**
     * Show admin notice
     */
    showNotice: function(type, message) {
      const noticeClass = 'bitcoiniacs-admin-notice notice-' + type;
      const $notice = $('<div class="' + noticeClass + '"><p>' + message + '</p></div>');
      
      // Remove existing notices
      $('.bitcoiniacs-admin-notice').remove();
      
      // Add new notice
      $('.wrap h1').after($notice);
      
      // Auto-hide success notices
      if (type === 'success') {
        setTimeout(function() {
          $notice.fadeOut();
        }, 3000);
      }
      
      // Scroll to notice
      $('html, body').animate({
        scrollTop: $notice.offset().top - 50
      }, 300);
    },
    
    /**
     * Confirm dialog helper
     */
    confirm: function(message, callback) {
      if (confirm(message)) {
        callback();
      }
    }
  };

})(jQuery);
