<?php
/**
 * Verification script to check Manila ATM fixes
 */

// WordPress environment setup
require_once('wp-config.php');
require_once('wp-load.php');

echo "<h1>Manila ATM Fixes Verification</h1>\n";

// Check database updates
global $wpdb;
$table_name = $wpdb->prefix . 'bitcoiniacs_atm_locations';

echo "<h2>Database Check</h2>\n";

$manila_locations = $wpdb->get_results(
    "SELECT id, location_name, address, latitude, longitude FROM $table_name WHERE country = 'PH'"
);

if ($manila_locations) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>ID</th><th>Name</th><th>Address</th><th>Latitude</th><th>Longitude</th><th>Status</th></tr>\n";
    
    foreach ($manila_locations as $location) {
        $status = '';
        
        // Check if coordinates are different (not the old 14.5547, 121.0244)
        if ($location->latitude != 14.5547 || $location->longitude != 121.0244) {
            $status = "<span style='color: green;'>✓ Updated</span>";
        } else {
            $status = "<span style='color: red;'>✗ Still old coordinates</span>";
        }
        
        echo "<tr>\n";
        echo "<td>{$location->id}</td>\n";
        echo "<td>{$location->location_name}</td>\n";
        echo "<td>{$location->address}</td>\n";
        echo "<td>{$location->latitude}</td>\n";
        echo "<td>{$location->longitude}</td>\n";
        echo "<td>{$status}</td>\n";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p style='color: red;'>No Manila locations found in database.</p>\n";
}

// Check JavaScript file changes
echo "<h2>JavaScript File Check</h2>\n";

$js_file = 'wp-content/plugins/bitcoiniacs-atm-locator/assets/js/frontend.js';
if (file_exists($js_file)) {
    $js_content = file_get_contents($js_file);
    
    // Check for the updated renderCities function
    if (strpos($js_content, "location.country === 'PH'") !== false) {
        echo "<p style='color: green;'>✓ JavaScript updated to handle Philippines addresses</p>\n";
    } else {
        echo "<p style='color: red;'>✗ JavaScript NOT updated</p>\n";
    }
    
    if (strpos($js_content, "Philippines format:") !== false) {
        echo "<p style='color: green;'>✓ Philippines address format handling added</p>\n";
    } else {
        echo "<p style='color: red;'>✗ Philippines address format handling NOT added</p>\n";
    }
} else {
    echo "<p style='color: red;'>✗ JavaScript file not found</p>\n";
}

// Test city extraction logic
echo "<h2>City Extraction Test</h2>\n";

$test_addresses = array(
    'Sunette Tower, Makati Ave. cor Durban St., Makati City, Metro Manila, Philippines',
    '125 L.P. Leviste St, Salcedo Village, Makati City, Metro Manila, Philippines'
);

echo "<p>Testing city extraction from Philippines addresses:</p>\n";
echo "<ul>\n";

foreach ($test_addresses as $address) {
    // Simulate the JavaScript regex logic in PHP
    if (preg_match('/([^,]+),\s*([^,]+),\s*Philippines/', $address, $matches)) {
        $city = trim($matches[1]);
        echo "<li><strong>Address:</strong> {$address}<br><strong>Extracted City:</strong> <span style='color: green;'>{$city}</span></li>\n";
    } else {
        echo "<li><strong>Address:</strong> {$address}<br><strong>Extracted City:</strong> <span style='color: red;'>Failed to extract</span></li>\n";
    }
}

echo "</ul>\n";

echo "<h2>Summary</h2>\n";

$all_good = true;

// Check if both locations have different coordinates
$unique_coords = array();
foreach ($manila_locations as $location) {
    $coord_key = $location->latitude . ',' . $location->longitude;
    if (in_array($coord_key, $unique_coords)) {
        echo "<p style='color: red;'>✗ Duplicate coordinates found</p>\n";
        $all_good = false;
    } else {
        $unique_coords[] = $coord_key;
    }
}

if (count($unique_coords) == 2) {
    echo "<p style='color: green;'>✓ Both Manila locations have unique coordinates</p>\n";
}

if ($all_good && count($manila_locations) == 2) {
    echo "<p style='color: green; font-weight: bold;'>✓ All fixes appear to be working correctly!</p>\n";
    echo "<p>Next steps:</p>\n";
    echo "<ol>\n";
    echo "<li>Clear browser cache and test the frontend</li>\n";
    echo "<li>Navigate to Philippines → Metro Manila in the visual selector</li>\n";
    echo "<li>Verify that cities are now showing (should see 'Makati City')</li>\n";
    echo "<li>Check that both ATMs appear as separate markers on the map</li>\n";
    echo "</ol>\n";
} else {
    echo "<p style='color: red; font-weight: bold;'>✗ Some issues remain - please check the details above</p>\n";
}

echo "<p><strong>Note:</strong> You can delete this verification script after confirming everything works.</p>\n";
?>
