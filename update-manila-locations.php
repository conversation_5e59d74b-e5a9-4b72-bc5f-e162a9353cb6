<?php
/**
 * Temporary script to update Manila ATM locations with correct coordinates
 * Run this once to fix the existing database records
 */

// WordPress environment setup
require_once('wp-config.php');
require_once('wp-load.php');

// Load the database class
require_once('wp-content/plugins/bitcoiniacs-atm-locator/includes/class-database.php');

echo "<h1>Manila ATM Locations Update</h1>\n";

// Check if the plugin table exists
global $wpdb;
$table_name = $wpdb->prefix . 'bitcoiniacs_atm_locations';

$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

if (!$table_exists) {
    echo "<p style='color: red;'>Error: Plugin table does not exist. Please activate the plugin first.</p>\n";
    exit;
}

echo "<h2>Before Update</h2>\n";
echo "<p>Current Manila locations:</p>\n";

// Show current Manila locations
$current_locations = $wpdb->get_results(
    "SELECT id, location_name, address, latitude, longitude FROM $table_name WHERE country = 'PH'"
);

if ($current_locations) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>ID</th><th>Name</th><th>Address</th><th>Latitude</th><th>Longitude</th></tr>\n";
    foreach ($current_locations as $location) {
        echo "<tr>\n";
        echo "<td>{$location->id}</td>\n";
        echo "<td>{$location->location_name}</td>\n";
        echo "<td>{$location->address}</td>\n";
        echo "<td>{$location->latitude}</td>\n";
        echo "<td>{$location->longitude}</td>\n";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p>No Manila locations found.</p>\n";
}

// Run the update
echo "<h2>Running Update</h2>\n";

try {
    $result = BitcoiniacsATMDatabase::update_manila_locations();
    if ($result) {
        echo "<p style='color: green;'>✓ Manila locations updated successfully!</p>\n";
    } else {
        echo "<p style='color: red;'>✗ Update failed.</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>\n";
}

echo "<h2>After Update</h2>\n";
echo "<p>Updated Manila locations:</p>\n";

// Show updated Manila locations
$updated_locations = $wpdb->get_results(
    "SELECT id, location_name, address, latitude, longitude FROM $table_name WHERE country = 'PH'"
);

if ($updated_locations) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>ID</th><th>Name</th><th>Address</th><th>Latitude</th><th>Longitude</th></tr>\n";
    foreach ($updated_locations as $location) {
        echo "<tr>\n";
        echo "<td>{$location->id}</td>\n";
        echo "<td>{$location->location_name}</td>\n";
        echo "<td>{$location->address}</td>\n";
        echo "<td>{$location->latitude}</td>\n";
        echo "<td>{$location->longitude}</td>\n";
        echo "</tr>\n";
    }
    echo "</table>\n";
} else {
    echo "<p>No Manila locations found after update.</p>\n";
}

echo "<h2>Instructions</h2>\n";
echo "<p>If the update was successful:</p>\n";
echo "<ol>\n";
echo "<li>Clear any WordPress caching plugins</li>\n";
echo "<li>Clear browser cache (Ctrl+F5 or Cmd+Shift+R)</li>\n";
echo "<li>Test the plugin functionality</li>\n";
echo "<li>Delete this update script file for security</li>\n";
echo "</ol>\n";

echo "<p><strong>Note:</strong> You can safely delete this file after running it once.</p>\n";
?>
