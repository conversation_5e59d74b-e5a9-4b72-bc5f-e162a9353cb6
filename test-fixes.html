<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin ATM Locator - Test Fixes</title>
    
    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- Plugin CSS -->
    <link rel="stylesheet" href="bitcoiniacs-atm-locator/assets/css/frontend.css" />
    
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h2 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .console-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Bitcoin ATM Locator - Test Fixes</h1>
        
        <div class="test-section">
            <h2>Test 1: Map Tile Loading</h2>
            <p>Testing if OpenStreetMap tiles load properly without API keys.</p>
            <div id="test-map" style="height: 300px; width: 100%; border: 1px solid #ccc;"></div>
            <div id="map-status" class="status"></div>
        </div>
        
        <div class="test-section">
            <h2>Test 2: Regional Filtering</h2>
            <p>Testing if regions with 0 ATMs are hidden from the visual selector.</p>
            
            <!-- Mock Visual Selector -->
            <div class="bitcoiniacs-visual-selector">
                <div class="selector-header">
                    <h3>Select Location Visually</h3>
                    <p>Click on a region to explore ATM locations</p>
                </div>
                
                <div class="selector-tabs">
                    <button class="selector-tab active" data-level="country">Country</button>
                    <button class="selector-tab" data-level="region">Region</button>
                    <button class="selector-tab" data-level="city">City</button>
                </div>
                
                <div class="selector-content">
                    <div id="country-grid" class="selector-grid">
                        <div class="country-card" data-country="CA">
                            <span class="country-icon">🍁</span>
                            <h4>Canada</h4>
                            <p class="country-stats">29 ATMs</p>
                        </div>
                    </div>
                    
                    <div id="region-grid" class="selector-grid" style="display: none;">
                        <!-- Regions will be populated here -->
                    </div>
                    
                    <div id="city-grid" class="selector-grid" style="display: none;">
                        <!-- Cities will be populated here -->
                    </div>
                </div>
            </div>
            
            <div id="region-status" class="status"></div>
        </div>
        
        <div class="test-section">
            <h2>Console Output</h2>
            <div id="console-output" class="console-output"></div>
        </div>
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // Mock AJAX object for testing
        window.bitcoiniacs_atm_ajax = {
            ajax_url: '#',
            nonce: 'test-nonce'
        };
        
        // Console capture
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        const consoleOutput = document.getElementById('console-output');
        
        function addToConsole(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            consoleOutput.innerHTML += `<div>[${timestamp}] ${type}: ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole('LOG', args.join(' '));
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole('WARN', args.join(' '));
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole('ERROR', args.join(' '));
        };
        
        // Test 1: Map Loading
        function testMapLoading() {
            console.log('Testing map tile loading...');
            
            const map = L.map('test-map').setView([45.4215, -75.6972], 6); // Ottawa, Canada
            
            // Use the same tile configuration as the plugin
            const tileUrl = 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';
            const attribution = '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors';
            
            const tileLayer = L.tileLayer(tileUrl, {
                attribution: attribution,
                maxZoom: 18,
                subdomains: ['a', 'b', 'c']
            });
            
            let tilesLoaded = 0;
            let tilesErrored = 0;
            
            tileLayer.on('tileload', function() {
                tilesLoaded++;
                console.log('Tile loaded successfully. Total loaded:', tilesLoaded);
                if (tilesLoaded >= 5) {
                    document.getElementById('map-status').className = 'status success';
                    document.getElementById('map-status').textContent = `✓ Map tiles loading successfully! (${tilesLoaded} tiles loaded)`;
                }
            });
            
            tileLayer.on('tileerror', function(error) {
                tilesErrored++;
                console.error('Tile loading error:', error);
                document.getElementById('map-status').className = 'status error';
                document.getElementById('map-status').textContent = `✗ Map tiles failed to load! (${tilesErrored} errors)`;
            });
            
            tileLayer.addTo(map);
            
            // Set initial status
            document.getElementById('map-status').textContent = 'Loading map tiles...';
            
            // Timeout check
            setTimeout(() => {
                if (tilesLoaded === 0) {
                    document.getElementById('map-status').className = 'status error';
                    document.getElementById('map-status').textContent = '✗ No tiles loaded after 10 seconds - check network or tile server';
                }
            }, 10000);
        }
        
        // Test 2: Regional Filtering
        function testRegionalFiltering() {
            console.log('Testing regional filtering...');
            
            // Mock ATM data (based on actual database content)
            const mockATMData = [
                {province: 'AB', location_name: 'Test ATM 1'},
                {province: 'AB', location_name: 'Test ATM 2'},
                {province: 'AB', location_name: 'Test ATM 3'},
                {province: 'AB', location_name: 'Test ATM 4'},
                {province: 'AB', location_name: 'Test ATM 5'},
                {province: 'BC', location_name: 'Test ATM 6'},
                {province: 'BC', location_name: 'Test ATM 7'},
                // ... more BC ATMs (18 total)
                {province: 'ON', location_name: 'Test ATM 8'},
                {province: 'ON', location_name: 'Test ATM 9'},
                // ... more ON ATMs (6 total)
                // Note: No QC, NS, NB ATMs in the data
            ];
            
            // Simulate the regions from the plugin
            const regions = [
                { code: 'AB', name: 'Alberta', icon: '🏔️' },
                { code: 'BC', name: 'British Columbia', icon: '🌲' },
                { code: 'ON', name: 'Ontario', icon: '🏙️' },
                { code: 'QC', name: 'Quebec', icon: '🍁' },
                { code: 'NS', name: 'Nova Scotia', icon: '🌊' },
                { code: 'NB', name: 'New Brunswick', icon: '🦞' }
            ];
            
            // Create region cards
            const regionGrid = document.getElementById('region-grid');
            let html = '';
            regions.forEach(function(region) {
                html += `
                    <div class="region-card" data-region="${region.code}">
                        <span class="region-icon">${region.icon}</span>
                        <h4>${region.name}</h4>
                        <p class="region-stats">
                            <span class="atm-count" data-region="${region.code}">-</span> ATMs
                        </p>
                    </div>
                `;
            });
            regionGrid.innerHTML = html;
            
            // Count ATMs by province
            const counts = {};
            mockATMData.forEach(function(location) {
                counts[location.province] = (counts[location.province] || 0) + 1;
            });
            
            console.log('ATM counts by province:', counts);
            
            // Apply the same logic as the plugin
            let visibleRegions = 0;
            let hiddenRegions = 0;
            
            // First hide all regions
            $('[data-region]').hide();
            
            $('[data-region]').each(function() {
                const $regionCard = $(this);
                const region = $regionCard.data('region');
                const count = counts[region] || 0;
                
                console.log('Region:', region, 'Count:', count);
                
                $regionCard.find('.atm-count').text(count);
                
                // Only show regions with ATMs
                if (count > 0) {
                    console.log('Showing region with ATMs:', region, count);
                    $regionCard.show();
                    visibleRegions++;
                } else {
                    console.log('Hiding region with 0 ATMs:', region);
                    hiddenRegions++;
                }
            });
            
            // Show results
            const statusEl = document.getElementById('region-status');
            if (hiddenRegions > 0 && visibleRegions > 0) {
                statusEl.className = 'status success';
                statusEl.textContent = `✓ Regional filtering working! ${visibleRegions} regions shown, ${hiddenRegions} regions hidden (0 ATMs)`;
            } else if (hiddenRegions === 0) {
                statusEl.className = 'status error';
                statusEl.textContent = `✗ Regional filtering not working - all regions are visible`;
            } else {
                statusEl.className = 'status error';
                statusEl.textContent = `✗ Regional filtering issue - no regions visible`;
            }
            
            // Show the region grid
            regionGrid.style.display = 'grid';
        }
        
        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Starting tests...');
            testMapLoading();
            testRegionalFiltering();
        });
    </script>
</body>
</html>
