<?php
/**
 * Database operations for Bitcoiniacs ATM Locator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

class BitcoiniacsATMDatabase {
  
  /**
   * Create database tables
   */
  public static function create_tables() {
    global $wpdb;
    
    $table_name = BitcoiniacsATMLocator::$table_name;
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
      id mediumint(9) NOT NULL AUTO_INCREMENT,
      location_name varchar(255) NOT NULL,
      address text NOT NULL,
      province varchar(10) NOT NULL,
      country varchar(10) DEFAULT 'CA',
      latitude decimal(10, 8) NOT NULL,
      longitude decimal(11, 8) NOT NULL,
      status varchar(20) DEFAULT 'Active',
      can_buy tinyint(1) DEFAULT 1,
      can_sell tinyint(1) DEFAULT 0,
      operating_hours text,
      phone varchar(20),
      website varchar(255),
      notes text,
      created_at datetime DEFAULT CURRENT_TIMESTAMP,
      updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (id),
      KEY province (province),
      KEY status (status),
      KEY location (latitude, longitude)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    // Update version
    update_option('bitcoiniacs_atm_db_version', '1.0');
  }
  
  /**
   * Drop database tables
   */
  public static function drop_tables() {
    global $wpdb;
    
    $table_name = BitcoiniacsATMLocator::$table_name;
    $wpdb->query("DROP TABLE IF EXISTS $table_name");
    
    delete_option('bitcoiniacs_atm_db_version');
  }
  
  /**
   * Insert seed data from existing ATM locations
   */
  public static function insert_seed_data() {
    global $wpdb;
    
    $table_name = BitcoiniacsATMLocator::$table_name;
    
    // Check if data already exists
    $existing_count = $wpdb->get_var("SELECT COUNT(*) FROM $table_name");
    if ($existing_count > 0) {
      return; // Data already exists, don't duplicate
    }
    
    $seed_data = self::get_seed_data();
    
    foreach ($seed_data as $atm) {
      $wpdb->insert(
        $table_name,
        array(
          'location_name' => $atm['location_name'],
          'address' => $atm['address'],
          'province' => $atm['province'],
          'country' => $atm['country'],
          'latitude' => $atm['latitude'],
          'longitude' => $atm['longitude'],
          'status' => $atm['status'],
          'can_buy' => $atm['can_buy'],
          'can_sell' => $atm['can_sell']
        ),
        array(
          '%s', '%s', '%s', '%s', '%f', '%f', '%s', '%d', '%d'
        )
      );
    }
  }
  
  /**
   * Get seed data array from existing ATM locations
   */
  private static function get_seed_data() {
    return array(
      // Alberta locations
      array(
        'location_name' => 'Bitcoiniacs ATM - AK Grocery & Variety Store',
        'address' => '5008 Whitehorn Dr NE, Calgary, AB T1Y 1V1',
        'province' => 'AB',
        'country' => 'CA',
        'latitude' => 51.0917,
        'longitude' => -113.9654,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 1
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Foggy Gorilla Vape Shop Red Deer',
        'address' => '6842 50 Ave #5, Red Deer, AB T4N 4E3',
        'province' => 'AB',
        'country' => 'CA',
        'latitude' => 52.2755,
        'longitude' => -113.811,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 1
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Calgary North',
        'address' => '123 Edmonton Trail NE, Calgary, AB',
        'province' => 'AB',
        'country' => 'CA',
        'latitude' => 51.0486,
        'longitude' => -114.0708,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Edmonton West',
        'address' => '456 Jasper Ave, Edmonton, AB',
        'province' => 'AB',
        'country' => 'CA',
        'latitude' => 53.5444,
        'longitude' => -113.4909,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Lethbridge',
        'address' => '789 Mayor Magrath Dr S, Lethbridge, AB',
        'province' => 'AB',
        'country' => 'CA',
        'latitude' => 49.6951,
        'longitude' => -112.8384,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 1
      ),
      
      // British Columbia locations
      array(
        'location_name' => 'Bitcoiniacs ATM - Lougheed Mini Mart',
        'address' => '11842 207 St, Maple Ridge, BC V2X 1X5',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.2319,
        'longitude' => -122.4659,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 1
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Mamas Convenience Store',
        'address' => '1475 Ellis St, Kelowna, BC V1Y 9N3',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.8880,
        'longitude' => -119.4960,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Shell Gas Station',
        'address' => '34010 Lougheed Hwy, Mission, BC V2V 5X8',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.1321,
        'longitude' => -122.3320,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Waves Coffee Metrotown',
        'address' => '1020 - 4800 Kingsway, Burnaby, BC V5H 4J2',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.2237,
        'longitude' => -122.9984,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 1
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Waves Coffee VCC Clark',
        'address' => '1155 E Broadway, Vancouver, BC V6H 4B7',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.2577,
        'longitude' => -123.0890,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 1
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Waves Coffee Richmond',
        'address' => '3031 No 3 Rd #140, Richmond, BC V6X 2B3',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.1666,
        'longitude' => -123.1364,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 1
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Yaletown Deli',
        'address' => '499 Drake St, Vancouver, BC V6Z 2B9',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.2744,
        'longitude' => -123.1216,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 1
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Britannia Community Center',
        'address' => '1661 Napier St, Vancouver, BC V5L 4X4',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.2754,
        'longitude' => -123.0707,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),

      // More BC locations
      array(
        'location_name' => 'Bitcoiniacs ATM - Kingsway Shell',
        'address' => '3302 Kingsway, Vancouver, BC V5R 5K9',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.2324,
        'longitude' => -123.0343,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Husky Gas Station',
        'address' => '32988 1st Ave, Mission, BC V2V 1E7',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.1337,
        'longitude' => -122.3055,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Mobil Gas Station',
        'address' => '2408 W 41st Ave, Vancouver, BC V6M 2A7',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.2335,
        'longitude' => -123.1591,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Chevron Gas Station',
        'address' => '8580 Granville St, Vancouver, BC V6P 4Z6',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.2088,
        'longitude' => -123.1404,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Shell Gas Station Langley',
        'address' => '19705 Willowbrook Dr, Langley, BC V2Y 1A7',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.1042,
        'longitude' => -122.5898,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Chevron Gas Station Surrey',
        'address' => '12020 Nordel Way, Surrey, BC V3W 1P6',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.1189,
        'longitude' => -122.8447,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Shell Gas Station Burnaby',
        'address' => '7155 Kingsway, Burnaby, BC V5E 2V1',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.2329,
        'longitude' => -122.9598,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Chevron Gas Station Hastings',
        'address' => '3695 E Hastings St, Vancouver, BC V5K 2A7',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.2812,
        'longitude' => -123.0268,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Esso Gas Station',
        'address' => '1398 Kingsway, Vancouver, BC V5V 3E2',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.2441,
        'longitude' => -123.0707,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Petro Canada',
        'address' => '4302 Main St, Vancouver, BC V5V 3P9',
        'province' => 'BC',
        'country' => 'CA',
        'latitude' => 49.2488,
        'longitude' => -123.1003,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),

      // Ontario locations
      array(
        'location_name' => 'Bitcoiniacs ATM - Mister Convenience Peterborough',
        'address' => '608 Orpington Rd, Peterborough, ON K9J 4A4',
        'province' => 'ON',
        'country' => 'CA',
        'latitude' => 44.3091,
        'longitude' => -78.3197,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 1
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Esso Gas Station Ancaster',
        'address' => '1490 Golf Links Rd, Ancaster, ON L9K 1H9',
        'province' => 'ON',
        'country' => 'CA',
        'latitude' => 43.2176,
        'longitude' => -79.9739,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Shell Gas Station Hamilton',
        'address' => '1055 Fennell Ave E, Hamilton, ON L8T 1S2',
        'province' => 'ON',
        'country' => 'CA',
        'latitude' => 43.2205,
        'longitude' => -79.8544,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Pioneer Gas Station',
        'address' => '7063 Tecumseh Rd E, Windsor, ON N8T 1E7',
        'province' => 'ON',
        'country' => 'CA',
        'latitude' => 42.2808,
        'longitude' => -82.9199,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Shell Gas Station Toronto',
        'address' => '2550 Victoria Park Ave, Scarborough, ON M1T 1A4',
        'province' => 'ON',
        'country' => 'CA',
        'latitude' => 43.7615,
        'longitude' => -79.3006,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Petro Canada Mississauga',
        'address' => '6677 Mississauga Rd, Mississauga, ON L5N 2L3',
        'province' => 'ON',
        'country' => 'CA',
        'latitude' => 43.5890,
        'longitude' => -79.7330,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 0
      ),

      // Manila locations
      array(
        'location_name' => 'Bitcoiniacs ATM - Sunette Tower',
        'address' => 'Sunette Tower, Makati Ave. cor Durban St., Makati City, Metro Manila, Philippines',
        'province' => 'MNL',
        'country' => 'PH',
        'latitude' => 14.5580,
        'longitude' => 121.0190,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 1
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - V Corporate Center',
        'address' => '125 L.P. Leviste St, Salcedo Village, Makati City, Metro Manila, Philippines',
        'province' => 'MNL',
        'country' => 'PH',
        'latitude' => 14.5520,
        'longitude' => 121.0280,
        'status' => 'Active',
        'can_buy' => 1,
        'can_sell' => 1
      )
    );
  }
  
  /**
   * Get all ATM locations
   */
  public static function get_all_locations($filters = array()) {
    global $wpdb;
    
    $table_name = BitcoiniacsATMLocator::$table_name;
    $where_clauses = array("status = 'Active'");
    $where_values = array();
    
    // Apply filters
    if (!empty($filters['province']) && $filters['province'] !== 'all') {
      $where_clauses[] = "province = %s";
      $where_values[] = $filters['province'];
    }
    
    if (!empty($filters['country']) && $filters['country'] !== 'all') {
      $where_clauses[] = "country = %s";
      $where_values[] = $filters['country'];
    }
    
    if (!empty($filters['service'])) {
      switch ($filters['service']) {
        case 'buy':
          $where_clauses[] = "can_buy = 1";
          break;
        case 'sell':
          $where_clauses[] = "can_sell = 1";
          break;
        case 'both':
          $where_clauses[] = "can_buy = 1 AND can_sell = 1";
          break;
      }
    }
    
    if (!empty($filters['search'])) {
      $where_clauses[] = "(location_name LIKE %s OR address LIKE %s)";
      $search_term = '%' . $wpdb->esc_like($filters['search']) . '%';
      $where_values[] = $search_term;
      $where_values[] = $search_term;
    }
    
    $where_sql = implode(' AND ', $where_clauses);
    $sql = "SELECT * FROM $table_name WHERE $where_sql ORDER BY location_name";
    
    if (!empty($where_values)) {
      $sql = $wpdb->prepare($sql, $where_values);
    }
    
    return $wpdb->get_results($sql);
  }
  
  /**
   * Get single ATM location
   */
  public static function get_location($id) {
    global $wpdb;
    
    $table_name = BitcoiniacsATMLocator::$table_name;
    return $wpdb->get_row($wpdb->prepare(
      "SELECT * FROM $table_name WHERE id = %d",
      $id
    ));
  }
  
  /**
   * Insert new ATM location
   */
  public static function insert_location($data) {
    global $wpdb;
    
    $table_name = BitcoiniacsATMLocator::$table_name;
    
    $result = $wpdb->insert(
      $table_name,
      $data,
      array('%s', '%s', '%s', '%s', '%f', '%f', '%s', '%d', '%d', '%s', '%s', '%s', '%s')
    );
    
    if ($result === false) {
      return false;
    }
    
    return $wpdb->insert_id;
  }
  
  /**
   * Update ATM location
   */
  public static function update_location($id, $data) {
    global $wpdb;
    
    $table_name = BitcoiniacsATMLocator::$table_name;
    
    return $wpdb->update(
      $table_name,
      $data,
      array('id' => $id),
      array('%s', '%s', '%s', '%s', '%f', '%f', '%s', '%d', '%d', '%s', '%s', '%s', '%s'),
      array('%d')
    );
  }
  
  /**
   * Delete ATM location
   */
  public static function delete_location($id) {
    global $wpdb;
    
    $table_name = BitcoiniacsATMLocator::$table_name;
    
    return $wpdb->delete(
      $table_name,
      array('id' => $id),
      array('%d')
    );
  }
  
  /**
   * Get provinces/states list
   */
  public static function get_provinces() {
    global $wpdb;

    $table_name = BitcoiniacsATMLocator::$table_name;

    return $wpdb->get_results(
      "SELECT DISTINCT province, country FROM $table_name WHERE status = 'Active' ORDER BY country, province"
    );
  }

  /**
   * Update Manila ATM locations with correct coordinates and addresses
   */
  public static function update_manila_locations() {
    global $wpdb;

    $table_name = BitcoiniacsATMLocator::$table_name;

    // Update Sunette Tower location
    $wpdb->update(
      $table_name,
      array(
        'location_name' => 'Bitcoiniacs ATM - Sunette Tower',
        'address' => 'Sunette Tower, Makati Ave. cor Durban St., Makati City, Metro Manila, Philippines',
        'latitude' => 14.5580,
        'longitude' => 121.0190
      ),
      array(
        'location_name' => 'Bitcoiniacs ATM - Sunette tower',
        'country' => 'PH'
      ),
      array('%s', '%s', '%f', '%f'),
      array('%s', '%s')
    );

    // Update V Corporate Center location
    $wpdb->update(
      $table_name,
      array(
        'location_name' => 'Bitcoiniacs ATM - V Corporate Center',
        'address' => '125 L.P. Leviste St, Salcedo Village, Makati City, Metro Manila, Philippines',
        'latitude' => 14.5520,
        'longitude' => 121.0280
      ),
      array(
        'address' => '125 L.P. Leviste St, Salcedo Village, Makati, 1227, Philippines',
        'country' => 'PH'
      ),
      array('%s', '%s', '%f', '%f'),
      array('%s', '%s')
    );

    return true;
  }
}
