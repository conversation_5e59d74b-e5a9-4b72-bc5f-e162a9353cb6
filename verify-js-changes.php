<?php
/**
 * Verification script to check if JavaScript changes are present
 */

// Read the JavaScript file
$js_file = 'bitcoiniacs-atm-locator/assets/js/frontend.js';
$js_content = file_get_contents($js_file);

echo "<h1>JavaScript File Verification</h1>\n";
echo "<p><strong>File:</strong> $js_file</p>\n";
echo "<p><strong>File size:</strong> " . filesize($js_file) . " bytes</p>\n";
echo "<p><strong>Last modified:</strong> " . date('Y-m-d H:i:s', filemtime($js_file)) . "</p>\n";

echo "<h2>Key Changes Verification</h2>\n";

// Check for OpenStreetMap tile URL
if (strpos($js_content, 'tile.openstreetmap.org') !== false) {
    echo "<p style='color: green;'>✓ OpenStreetMap tile URL found</p>\n";
} else {
    echo "<p style='color: red;'>✗ OpenStreetMap tile URL NOT found</p>\n";
}

// Check for simplified tile layer function
if (strpos($js_content, 'Use OpenStreetMap for all styles') !== false) {
    echo "<p style='color: green;'>✓ Simplified tile layer comment found</p>\n";
} else {
    echo "<p style='color: red;'>✗ Simplified tile layer comment NOT found</p>\n";
}

// Check for regional filtering logic
if (strpos($js_content, 'First hide all regions') !== false) {
    echo "<p style='color: green;'>✓ Regional filtering logic found</p>\n";
} else {
    echo "<p style='color: red;'>✗ Regional filtering logic NOT found</p>\n";
}

// Check for show only regions with ATMs logic
if (strpos($js_content, 'Only show regions with ATMs') !== false) {
    echo "<p style='color: green;'>✓ Show only regions with ATMs logic found</p>\n";
} else {
    echo "<p style='color: red;'>✗ Show only regions with ATMs logic NOT found</p>\n";
}

// Check that old CartoDB references are removed
if (strpos($js_content, 'cartocdn.com') !== false) {
    echo "<p style='color: orange;'>⚠ Old CartoDB reference still found (should be removed)</p>\n";
} else {
    echo "<p style='color: green;'>✓ Old CartoDB references removed</p>\n";
}

echo "<h2>Plugin Version Check</h2>\n";

// Read the main plugin file
$plugin_file = 'bitcoiniacs-atm-locator/bitcoiniacs-atm-locator.php';
$plugin_content = file_get_contents($plugin_file);

// Check version number
if (strpos($plugin_content, "Version: 1.0.1") !== false) {
    echo "<p style='color: green;'>✓ Plugin version updated to 1.0.1</p>\n";
} else {
    echo "<p style='color: red;'>✗ Plugin version NOT updated</p>\n";
}

// Check version constant
if (strpos($plugin_content, "define('BITCOINIACS_ATM_VERSION', '1.0.1')") !== false) {
    echo "<p style='color: green;'>✓ Version constant updated to 1.0.1</p>\n";
} else {
    echo "<p style='color: red;'>✗ Version constant NOT updated</p>\n";
}

// Check for cache busting
if (strpos($plugin_content, 'filemtime') !== false) {
    echo "<p style='color: green;'>✓ Cache busting with filemtime found</p>\n";
} else {
    echo "<p style='color: red;'>✗ Cache busting NOT found</p>\n";
}

echo "<h2>Relevant Code Sections</h2>\n";

echo "<h3>Tile Layer Function</h3>\n";
echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>\n";
// Extract the addTileLayer function
$start = strpos($js_content, 'addTileLayer: function(map, style)');
$end = strpos($js_content, '},', $start) + 2;
if ($start !== false && $end !== false) {
    echo htmlspecialchars(substr($js_content, $start, $end - $start));
} else {
    echo "Function not found";
}
echo "</pre>\n";

echo "<h3>Regional Filtering Function</h3>\n";
echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>\n";
// Extract the regional filtering logic
$start = strpos($js_content, 'First hide all regions');
$end = strpos($js_content, '});', $start) + 3;
if ($start !== false && $end !== false) {
    // Go back to find the start of the logic
    $logic_start = strrpos(substr($js_content, 0, $start), 'const counts = {};');
    if ($logic_start !== false) {
        echo htmlspecialchars(substr($js_content, $logic_start, $end - $logic_start));
    } else {
        echo htmlspecialchars(substr($js_content, $start - 100, $end - $start + 100));
    }
} else {
    echo "Logic not found";
}
echo "</pre>\n";

echo "<h2>Instructions</h2>\n";
echo "<p>If all checks show green checkmarks, the changes are properly applied.</p>\n";
echo "<p>After verifying the changes:</p>\n";
echo "<ol>\n";
echo "<li>Deactivate and reactivate the plugin in WordPress admin</li>\n";
echo "<li>Clear any WordPress caching plugins</li>\n";
echo "<li>Clear browser cache (Ctrl+F5 or Cmd+Shift+R)</li>\n";
echo "<li>Test the plugin functionality</li>\n";
echo "</ol>\n";
?>
