<?php
/**
 * WordPress Cache Clearing Script
 * Run this script to clear various WordPress caches
 */

// This script should be run from WordPress root or with WordPress loaded
// For demonstration, we'll show the commands you should run

echo "<h1>WordPress Cache Clearing Instructions</h1>\n";

echo "<h2>Method 1: WordPress Admin (Recommended)</h2>\n";
echo "<ol>\n";
echo "<li><strong>Deactivate and Reactivate Plugin:</strong>\n";
echo "   <ul>\n";
echo "   <li>Go to WordPress Admin → Plugins</li>\n";
echo "   <li>Find 'Bitcoiniacs ATM Locator'</li>\n";
echo "   <li>Click 'Deactivate'</li>\n";
echo "   <li>Click 'Activate'</li>\n";
echo "   </ul></li>\n";

echo "<li><strong>Clear Caching Plugins:</strong>\n";
echo "   <ul>\n";
echo "   <li>If using WP Rocket: WP Rocket → Clear Cache</li>\n";
echo "   <li>If using W3 Total Cache: Performance → Purge All Caches</li>\n";
echo "   <li>If using WP Super Cache: Settings → WP Super Cache → Delete Cache</li>\n";
echo "   <li>If using LiteSpeed Cache: LiteSpeed Cache → Toolbox → Purge All</li>\n";
echo "   </ul></li>\n";

echo "<li><strong>Clear Browser Cache:</strong>\n";
echo "   <ul>\n";
echo "   <li>Chrome/Edge: Ctrl+Shift+R (Windows) or Cmd+Shift+R (Mac)</li>\n";
echo "   <li>Firefox: Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)</li>\n";
echo "   <li>Or open Developer Tools (F12) → Network tab → check 'Disable cache'</li>\n";
echo "   </ul></li>\n";
echo "</ol>\n";

echo "<h2>Method 2: Direct File Check</h2>\n";
echo "<p>You can verify the JavaScript is loading correctly by:</p>\n";
echo "<ol>\n";
echo "<li>Open your WordPress site with the ATM locator</li>\n";
echo "<li>Right-click → 'View Page Source'</li>\n";
echo "<li>Search for 'bitcoiniacs-atm-frontend'</li>\n";
echo "<li>Look for a line like: <code>&lt;script src='...frontend.js?ver=TIMESTAMP'&gt;</code></li>\n";
echo "<li>The version should be a timestamp (like 1719123747) not 1.0.0</li>\n";
echo "</ol>\n";

echo "<h2>Method 3: Direct JavaScript URL Test</h2>\n";
echo "<p>Test the JavaScript file directly:</p>\n";
echo "<ol>\n";
echo "<li>Go to: <code>https://yoursite.com/wp-content/plugins/bitcoiniacs-atm-locator/assets/js/frontend.js</code></li>\n";
echo "<li>Search for 'tile.openstreetmap.org' - it should be found</li>\n";
echo "<li>Search for 'First hide all regions' - it should be found</li>\n";
echo "<li>Search for 'cartocdn.com' - it should NOT be found</li>\n";
echo "</ol>\n";

echo "<h2>Method 4: WordPress CLI (if available)</h2>\n";
echo "<pre>\n";
echo "# Clear object cache\n";
echo "wp cache flush\n\n";
echo "# Clear transients\n";
echo "wp transient delete --all\n\n";
echo "# Deactivate and reactivate plugin\n";
echo "wp plugin deactivate bitcoiniacs-atm-locator\n";
echo "wp plugin activate bitcoiniacs-atm-locator\n";
echo "</pre>\n";

echo "<h2>Verification Steps</h2>\n";
echo "<p>After clearing caches, verify the fixes work:</p>\n";
echo "<ol>\n";
echo "<li><strong>Map Loading:</strong> Maps should show OpenStreetMap tiles instead of empty/broken areas</li>\n";
echo "<li><strong>Regional Filtering:</strong> Only Alberta, British Columbia, and Ontario should show for Canada</li>\n";
echo "<li><strong>Hidden Regions:</strong> Quebec, Nova Scotia, and New Brunswick should be hidden (0 ATMs)</li>\n";
echo "</ol>\n";

echo "<h2>Debug Information</h2>\n";
echo "<p><strong>Plugin Version:</strong> 1.0.1</p>\n";
echo "<p><strong>JavaScript File Modified:</strong> " . date('Y-m-d H:i:s', filemtime('bitcoiniacs-atm-locator/assets/js/frontend.js')) . "</p>\n";
echo "<p><strong>Cache Busting:</strong> Enabled (using file modification time)</p>\n";

echo "<h2>If Issues Persist</h2>\n";
echo "<p>If the plugin still shows old behavior after following all steps:</p>\n";
echo "<ol>\n";
echo "<li>Check if there are multiple copies of the plugin installed</li>\n";
echo "<li>Verify you're editing the correct plugin directory</li>\n";
echo "<li>Check server-side caching (CloudFlare, hosting provider cache)</li>\n";
echo "<li>Try accessing the site from an incognito/private browser window</li>\n";
echo "</ol>\n";

echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; }\n";
echo "h1, h2 { color: #333; }\n";
echo "code { background: #f5f5f5; padding: 2px 4px; border-radius: 3px; }\n";
echo "pre { background: #f5f5f5; padding: 10px; border-radius: 5px; border: 1px solid #ddd; }\n";
echo "ol, ul { line-height: 1.6; }\n";
echo "</style>\n";
?>
