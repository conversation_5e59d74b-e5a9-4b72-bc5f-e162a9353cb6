/**
 * Frontend styles for Bitcoiniacs ATM Locator
 */

/* Container Styles */
.bitcoiniacs-atm-locator-container {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  max-width: 100%;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border-radius: 20px;
  color: #ffffff;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Header Styles */
.bitcoiniacs-atm-header {
  text-align: center;
  margin-bottom: 30px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.bitcoiniacs-atm-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite alternate;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  100% { background-position: 100% 50%; }
}

/* Controls Styles */
.bitcoiniacs-atm-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 25px;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.05);
  padding: 20px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.bitcoiniacs-atm-control-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
  min-width: 200px;
}

.bitcoiniacs-atm-control-group label {
  font-size: 0.9rem;
  color: #b0b0b0;
  font-weight: 500;
}

.bitcoiniacs-atm-controls select,
.bitcoiniacs-atm-controls input,
.bitcoiniacs-atm-controls button {
  background: rgba(26, 26, 46, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  color: #ffffff;
  font-size: 0.95rem;
  transition: all 0.3s ease;
}

.bitcoiniacs-atm-controls select option {
  background: #1a1a2e;
  color: #ffffff;
  padding: 8px;
}

.bitcoiniacs-atm-controls select:focus,
.bitcoiniacs-atm-controls input:focus {
  outline: none;
  border-color: #4ecdc4;
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
  transform: translateY(-2px);
}

.bitcoiniacs-atm-controls input::placeholder {
  color: #888;
}

.bitcoiniacs-atm-controls button {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  border: none;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.bitcoiniacs-atm-controls button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(78, 205, 196, 0.3);
}

.bitcoiniacs-atm-controls button:active {
  transform: translateY(-1px);
}

.bitcoiniacs-atm-controls button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.bitcoiniacs-btn-locate {
  background: linear-gradient(45deg, #45b7d1, #96ceb4) !important;
}

.bitcoiniacs-btn-reset {
  background: linear-gradient(45deg, #6c757d, #495057) !important;
}

/* Map Container Styles */
.bitcoiniacs-atm-map-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.bitcoiniacs-atm-map {
  width: 100%;
  height: 600px;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: #1a1a2e;
}

/* Leaflet Popup Customization */
.leaflet-popup-content-wrapper {
  background: rgba(26, 26, 46, 0.95) !important;
  color: #ffffff !important;
  border-radius: 12px !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.leaflet-popup-content {
  color: #ffffff !important;
  margin: 15px !important;
}

.leaflet-popup-tip {
  background: rgba(26, 26, 46, 0.95) !important;
}

.popup-title {
  font-weight: bold;
  color: #4ecdc4;
  margin-bottom: 8px;
  font-size: 1.1rem;
}

.popup-address {
  margin-bottom: 10px;
  color: #e0e0e0;
}

.popup-features {
  display: flex;
  gap: 10px;
  margin-top: 10px;
  flex-wrap: wrap;
}

.feature-tag {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
}

.distance-info {
  margin-top: 8px;
  font-size: 0.9rem;
  color: #96ceb4;
  font-weight: 500;
}

/* Loading and Error States */
.bitcoiniacs-loading {
  text-align: center;
  padding: 40px;
  color: #4ecdc4;
  background: rgba(26, 26, 46, 0.9);
  border-radius: 15px;
  margin: 20px;
}

.bitcoiniacs-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(78, 205, 196, 0.3);
  border-radius: 50%;
  border-top-color: #4ecdc4;
  animation: spin 1s ease-in-out infinite;
  margin-right: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.bitcoiniacs-error-message {
  text-align: center;
  padding: 20px;
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 15px;
  margin: 20px;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

/* Custom Marker Styles */
.custom-marker {
  background: none !important;
  border: none !important;
  font-size: 24px;
  text-align: center;
  line-height: 30px;
  filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.7));
}

.user-marker {
  background: none !important;
  border: none !important;
  font-size: 24px;
  text-align: center;
  line-height: 30px;
  filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.7));
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .bitcoiniacs-atm-locator-container {
    padding: 15px;
  }
  
  .bitcoiniacs-atm-header h1 {
    font-size: 2rem;
  }
  
  .bitcoiniacs-atm-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .bitcoiniacs-atm-control-group {
    min-width: auto;
  }
  
  .bitcoiniacs-atm-map {
    height: 500px;
  }
  
  .popup-features {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .bitcoiniacs-atm-locator-container {
    padding: 10px;
  }
  
  .bitcoiniacs-atm-header {
    padding: 20px;
  }
  
  .bitcoiniacs-atm-header h1 {
    font-size: 1.8rem;
  }
  
  .bitcoiniacs-atm-controls {
    padding: 15px;
  }
  
  .bitcoiniacs-atm-map {
    height: 400px;
  }
}

/* Light Theme Override */
.bitcoiniacs-atm-light-theme {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
  color: #212529;
}

.bitcoiniacs-atm-light-theme .bitcoiniacs-atm-header {
  background: rgba(0, 0, 0, 0.05);
}

.bitcoiniacs-atm-light-theme .bitcoiniacs-atm-controls {
  background: rgba(0, 0, 0, 0.05);
}

.bitcoiniacs-atm-light-theme .bitcoiniacs-atm-controls select,
.bitcoiniacs-atm-light-theme .bitcoiniacs-atm-controls input {
  background: rgba(255, 255, 255, 0.9);
  color: #212529;
  border-color: rgba(0, 0, 0, 0.2);
}

.bitcoiniacs-atm-light-theme .bitcoiniacs-atm-control-group label {
  color: #6c757d;
}
