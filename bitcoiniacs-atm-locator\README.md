# Bitcoiniacs ATM Locator WordPress Plugin

A comprehensive Bitcoin ATM locator plugin for WordPress with admin management, visual map interface, and Elementor integration.

## Features

- **Interactive Map**: Powered by Leaflet with multiple map styles (dark, light, satellite)
- **Visual Location Selector**: Hierarchical country → region → city selection
- **Admin Management**: Full CRUD operations for ATM locations
- **Shortcode Support**: Easy integration with any page or post
- **Elementor Widget**: Native Elementor integration for page builders
- **Geolocation**: Find nearest ATMs using user's location
- **Filtering**: Filter by province, service type, and search
- **Responsive Design**: Mobile-friendly interface
- **REST API**: RESTful endpoints for external integrations
- **Seed Data**: Pre-populated with Canadian and international ATM locations

## Installation

### Manual Installation

1. Download the plugin files
2. Upload the `bitcoiniacs-atm-locator` folder to `/wp-content/plugins/`
3. Activate the plugin through the 'Plugins' menu in WordPress
4. The plugin will automatically create the database table and populate seed data

### WordPress Admin Installation

1. Go to Plugins → Add New
2. Upload the plugin zip file
3. Activate the plugin
4. Navigate to ATM Locations in the admin menu

## Usage

### Shortcode

Use the shortcode `[bitcoiniacs_atm_locator]` to display the ATM locator on any page or post.

#### Shortcode Parameters

- `height` - Map height in pixels (default: 600)
- `zoom` - Initial zoom level (default: 4)
- `center_lat` - Center latitude (default: 53.7267)
- `center_lng` - Center longitude (default: -127.6476)
- `province` - Filter by province (e.g., "BC")
- `country` - Filter by country (e.g., "CA")
- `service` - Filter by service type ("buy", "sell", "both")
- `show_filters` - Show filter controls (default: true)
- `show_search` - Show search box (default: true)
- `show_locate_btn` - Show "Find Nearest" button (default: true)
- `show_visual_selector` - Show visual location selector (default: true)
- `map_style` - Map style ("dark", "light", "satellite")

#### Examples

```
[bitcoiniacs_atm_locator]
[bitcoiniacs_atm_locator height="500" zoom="6" province="BC"]
[bitcoiniacs_atm_locator show_visual_selector="false" map_style="light"]
```

### Elementor Widget

1. Edit a page with Elementor
2. Search for "Bitcoin ATM Locator" widget
3. Drag and drop to your desired location
4. Configure settings in the widget panel

### Admin Management

#### Adding New Locations

1. Go to ATM Locations → Add New
2. Fill in the required fields:
   - Location Name
   - Address
   - Province/State
   - Country
   - Latitude/Longitude (use "Get Coordinates" button for auto-geocoding)
3. Configure services (Buy/Sell Bitcoin)
4. Set status and additional information
5. Save the location

#### Managing Existing Locations

1. Go to ATM Locations → All Locations
2. View, edit, or delete existing locations
3. Use bulk actions for multiple locations
4. Filter and search through locations

#### Settings

1. Go to ATM Locations → Settings
2. Configure default map settings:
   - Default zoom level
   - Default center coordinates
   - Map style preference

## REST API

The plugin provides REST API endpoints for external integrations:

### Get All Locations
```
GET /wp-json/bitcoiniacs-atm/v1/locations
```

Parameters:
- `province` - Filter by province
- `country` - Filter by country
- `service` - Filter by service type
- `search` - Search term
- `lat` - User latitude for distance calculation
- `lng` - User longitude for distance calculation
- `radius` - Search radius in kilometers

### Get Single Location
```
GET /wp-json/bitcoiniacs-atm/v1/locations/{id}
```

### Get Provinces
```
GET /wp-json/bitcoiniacs-atm/v1/provinces
```

## Database Schema

The plugin creates a custom table `wp_bitcoiniacs_atm_locations` with the following structure:

- `id` - Primary key
- `location_name` - ATM location name
- `address` - Full address
- `province` - Province/state code
- `country` - Country code
- `latitude` - Latitude coordinate
- `longitude` - Longitude coordinate
- `status` - Status (Active, Inactive, Maintenance)
- `can_buy` - Can buy Bitcoin (boolean)
- `can_sell` - Can sell Bitcoin (boolean)
- `operating_hours` - Operating hours text
- `phone` - Contact phone number
- `website` - Website URL
- `notes` - Additional notes
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp

## Customization

### Styling

The plugin includes comprehensive CSS that can be customized:

- Frontend styles: `/assets/css/frontend.css`
- Admin styles: `/assets/css/admin.css`

### JavaScript

Frontend functionality can be extended:

- Frontend JS: `/assets/js/frontend.js`
- Admin JS: `/assets/js/admin.js`

### Hooks and Filters

The plugin provides various WordPress hooks for customization:

```php
// Modify default shortcode attributes
add_filter('bitcoiniacs_atm_shortcode_defaults', function($defaults) {
    $defaults['map_style'] = 'light';
    return $defaults;
});

// Customize location data before display
add_filter('bitcoiniacs_atm_location_data', function($location) {
    // Modify location data
    return $location;
});
```

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- MySQL 5.6 or higher

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Troubleshooting

### Map Not Loading

1. Check browser console for JavaScript errors
2. Verify Leaflet library is loading correctly
3. Check network connectivity
4. Try different map style in settings

### Geocoding Not Working

1. Ensure internet connectivity
2. Check if OpenStreetMap Nominatim service is accessible
3. Try entering coordinates manually

### Locations Not Appearing

1. Verify locations are set to "Active" status
2. Check filter settings
3. Ensure coordinates are valid
4. Check browser console for AJAX errors

## Support

For support and feature requests, please contact the development team or create an issue in the project repository.

## License

This plugin is licensed under the GPL v2 or later.

## Changelog

### Version 1.0.0
- Initial release
- Interactive map with Leaflet
- Visual location selector
- Admin management interface
- Elementor integration
- REST API endpoints
- Responsive design
- Seed data with Canadian and international locations
