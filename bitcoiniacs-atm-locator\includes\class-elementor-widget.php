<?php
/**
 * Elementor Widget for Bitcoiniacs ATM Locator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

class BitcoiniacsATMElementorWidget extends \Elementor\Widget_Base {
  
  /**
   * Get widget name
   */
  public function get_name() {
    return 'bitcoiniacs_atm_locator';
  }
  
  /**
   * Get widget title
   */
  public function get_title() {
    return __('Bitcoin ATM Locator', 'bitcoiniacs-atm-locator');
  }
  
  /**
   * Get widget icon
   */
  public function get_icon() {
    return 'eicon-google-maps';
  }
  
  /**
   * Get widget categories
   */
  public function get_categories() {
    return ['general'];
  }
  
  /**
   * Get widget keywords
   */
  public function get_keywords() {
    return ['bitcoin', 'atm', 'map', 'locator', 'cryptocurrency'];
  }
  
  /**
   * Register widget controls
   */
  protected function _register_controls() {
    
    // Content Section
    $this->start_controls_section(
      'content_section',
      [
        'label' => __('Map Settings', 'bitcoiniacs-atm-locator'),
        'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
      ]
    );
    
    $this->add_control(
      'map_height',
      [
        'label' => __('Map Height', 'bitcoiniacs-atm-locator'),
        'type' => \Elementor\Controls_Manager::SLIDER,
        'size_units' => ['px'],
        'range' => [
          'px' => [
            'min' => 300,
            'max' => 1000,
            'step' => 50,
          ],
        ],
        'default' => [
          'unit' => 'px',
          'size' => 600,
        ],
      ]
    );
    
    $this->add_control(
      'initial_zoom',
      [
        'label' => __('Initial Zoom Level', 'bitcoiniacs-atm-locator'),
        'type' => \Elementor\Controls_Manager::SLIDER,
        'range' => [
          'px' => [
            'min' => 1,
            'max' => 18,
            'step' => 1,
          ],
        ],
        'default' => [
          'size' => get_option('bitcoiniacs_atm_default_zoom', 4),
        ],
      ]
    );
    
    $this->add_control(
      'center_latitude',
      [
        'label' => __('Center Latitude', 'bitcoiniacs-atm-locator'),
        'type' => \Elementor\Controls_Manager::NUMBER,
        'default' => get_option('bitcoiniacs_atm_default_center_lat', 53.7267),
        'step' => 0.000001,
      ]
    );
    
    $this->add_control(
      'center_longitude',
      [
        'label' => __('Center Longitude', 'bitcoiniacs-atm-locator'),
        'type' => \Elementor\Controls_Manager::NUMBER,
        'default' => get_option('bitcoiniacs_atm_default_center_lng', -127.6476),
        'step' => 0.000001,
      ]
    );
    
    $this->add_control(
      'map_style',
      [
        'label' => __('Map Style', 'bitcoiniacs-atm-locator'),
        'type' => \Elementor\Controls_Manager::SELECT,
        'default' => get_option('bitcoiniacs_atm_map_style', 'dark'),
        'options' => [
          'dark' => __('Dark Theme', 'bitcoiniacs-atm-locator'),
          'light' => __('Light Theme', 'bitcoiniacs-atm-locator'),
          'satellite' => __('Satellite', 'bitcoiniacs-atm-locator'),
        ],
      ]
    );
    
    $this->end_controls_section();
    
    // Filter Section
    $this->start_controls_section(
      'filter_section',
      [
        'label' => __('Filters & Controls', 'bitcoiniacs-atm-locator'),
        'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
      ]
    );
    
    $this->add_control(
      'show_filters',
      [
        'label' => __('Show Filter Controls', 'bitcoiniacs-atm-locator'),
        'type' => \Elementor\Controls_Manager::SWITCHER,
        'label_on' => __('Show', 'bitcoiniacs-atm-locator'),
        'label_off' => __('Hide', 'bitcoiniacs-atm-locator'),
        'return_value' => 'true',
        'default' => 'true',
      ]
    );
    
    $this->add_control(
      'show_search',
      [
        'label' => __('Show Search Box', 'bitcoiniacs-atm-locator'),
        'type' => \Elementor\Controls_Manager::SWITCHER,
        'label_on' => __('Show', 'bitcoiniacs-atm-locator'),
        'label_off' => __('Hide', 'bitcoiniacs-atm-locator'),
        'return_value' => 'true',
        'default' => 'true',
        'condition' => [
          'show_filters' => 'true',
        ],
      ]
    );
    
    $this->add_control(
      'show_locate_btn',
      [
        'label' => __('Show "Find Nearest" Button', 'bitcoiniacs-atm-locator'),
        'type' => \Elementor\Controls_Manager::SWITCHER,
        'label_on' => __('Show', 'bitcoiniacs-atm-locator'),
        'label_off' => __('Hide', 'bitcoiniacs-atm-locator'),
        'return_value' => 'true',
        'default' => 'true',
        'condition' => [
          'show_filters' => 'true',
        ],
      ]
    );
    
    $provinces = BitcoiniacsATMDatabase::get_provinces();
    $province_options = ['all' => __('All Provinces', 'bitcoiniacs-atm-locator')];
    foreach ($provinces as $province) {
      $province_options[$province->province] = $province->province . ' (' . $province->country . ')';
    }
    
    $this->add_control(
      'default_province',
      [
        'label' => __('Default Province Filter', 'bitcoiniacs-atm-locator'),
        'type' => \Elementor\Controls_Manager::SELECT,
        'default' => 'all',
        'options' => $province_options,
      ]
    );
    
    $this->add_control(
      'default_service',
      [
        'label' => __('Default Service Filter', 'bitcoiniacs-atm-locator'),
        'type' => \Elementor\Controls_Manager::SELECT,
        'default' => 'all',
        'options' => [
          'all' => __('All Services', 'bitcoiniacs-atm-locator'),
          'buy' => __('Buy Only', 'bitcoiniacs-atm-locator'),
          'sell' => __('Sell Only', 'bitcoiniacs-atm-locator'),
          'both' => __('Buy & Sell', 'bitcoiniacs-atm-locator'),
        ],
      ]
    );
    
    $this->end_controls_section();
    
    // Style Section
    $this->start_controls_section(
      'style_section',
      [
        'label' => __('Style', 'bitcoiniacs-atm-locator'),
        'tab' => \Elementor\Controls_Manager::TAB_STYLE,
      ]
    );
    
    $this->add_control(
      'border_radius',
      [
        'label' => __('Border Radius', 'bitcoiniacs-atm-locator'),
        'type' => \Elementor\Controls_Manager::DIMENSIONS,
        'size_units' => ['px', '%'],
        'selectors' => [
          '{{WRAPPER}} .bitcoiniacs-atm-map' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
        ],
      ]
    );
    
    $this->add_group_control(
      \Elementor\Group_Control_Box_Shadow::get_type(),
      [
        'name' => 'map_box_shadow',
        'label' => __('Box Shadow', 'bitcoiniacs-atm-locator'),
        'selector' => '{{WRAPPER}} .bitcoiniacs-atm-map',
      ]
    );
    
    $this->end_controls_section();
  }
  
  /**
   * Render widget output on the frontend
   */
  protected function render() {
    $settings = $this->get_settings_for_display();
    
    $shortcode_atts = array(
      'height' => $settings['map_height']['size'],
      'zoom' => $settings['initial_zoom']['size'],
      'center_lat' => $settings['center_latitude'],
      'center_lng' => $settings['center_longitude'],
      'map_style' => $settings['map_style'],
      'show_filters' => $settings['show_filters'],
      'show_search' => $settings['show_search'],
      'show_locate_btn' => $settings['show_locate_btn'],
      'province' => $settings['default_province'],
      'service' => $settings['default_service']
    );
    
    $shortcode = new BitcoiniacsATMShortcode();
    echo $shortcode->render_shortcode($shortcode_atts);
  }
  
  /**
   * Render widget output in the editor
   */
  protected function _content_template() {
    ?>
    <div class="bitcoiniacs-atm-locator-container">
      <div class="bitcoiniacs-atm-controls" style="margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
        <p style="margin: 0; color: #666; text-align: center;">
          <i class="eicon-google-maps" style="margin-right: 8px;"></i>
          <?php _e('Bitcoin ATM Locator will be displayed here on the frontend', 'bitcoiniacs-atm-locator'); ?>
        </p>
      </div>
      <div class="bitcoiniacs-atm-map-container">
        <div class="bitcoiniacs-atm-map" style="height: {{ settings.map_height.size }}px; background: #e9ecef; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #6c757d;">
          <div style="text-align: center;">
            <i class="eicon-google-maps" style="font-size: 48px; margin-bottom: 10px; display: block;"></i>
            <?php _e('Interactive Map Preview', 'bitcoiniacs-atm-locator'); ?>
          </div>
        </div>
      </div>
    </div>
    <?php
  }
}
