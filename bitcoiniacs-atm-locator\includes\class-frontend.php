<?php
/**
 * Frontend functionality for Bitcoiniacs ATM Locator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

class BitcoiniacsATMFrontend {
  
  /**
   * Constructor
   */
  public function __construct() {
    add_action('wp_ajax_bitcoiniacs_atm_get_locations', array($this, 'ajax_get_locations'));
    add_action('wp_ajax_nopriv_bitcoiniacs_atm_get_locations', array($this, 'ajax_get_locations'));
    
    add_action('wp_ajax_bitcoiniacs_atm_search_locations', array($this, 'ajax_search_locations'));
    add_action('wp_ajax_nopriv_bitcoiniacs_atm_search_locations', array($this, 'ajax_search_locations'));
  }
  
  /**
   * AJAX handler - Get locations
   */
  public function ajax_get_locations() {
    check_ajax_referer('bitcoiniacs_atm_nonce', 'nonce');
    
    $filters = array();
    
    if (!empty($_POST['province']) && $_POST['province'] !== 'all') {
      $filters['province'] = sanitize_text_field($_POST['province']);
    }
    
    if (!empty($_POST['country']) && $_POST['country'] !== 'all') {
      $filters['country'] = sanitize_text_field($_POST['country']);
    }
    
    if (!empty($_POST['service']) && $_POST['service'] !== 'all') {
      $filters['service'] = sanitize_text_field($_POST['service']);
    }
    
    if (!empty($_POST['search'])) {
      $filters['search'] = sanitize_text_field($_POST['search']);
    }
    
    $locations = BitcoiniacsATMDatabase::get_all_locations($filters);
    
    // Format locations for frontend
    $formatted_locations = array();
    foreach ($locations as $location) {
      $formatted_locations[] = array(
        'id' => intval($location->id),
        'name' => $location->location_name,
        'address' => $location->address,
        'province' => $location->province,
        'country' => $location->country,
        'latitude' => floatval($location->latitude),
        'longitude' => floatval($location->longitude),
        'status' => $location->status,
        'can_buy' => (bool) $location->can_buy,
        'can_sell' => (bool) $location->can_sell,
        'operating_hours' => $location->operating_hours,
        'phone' => $location->phone,
        'website' => $location->website,
        'notes' => $location->notes
      );
    }
    
    wp_send_json_success($formatted_locations);
  }
  
  /**
   * AJAX handler - Search locations
   */
  public function ajax_search_locations() {
    check_ajax_referer('bitcoiniacs_atm_nonce', 'nonce');
    
    $search_term = sanitize_text_field($_POST['search']);
    $user_lat = isset($_POST['user_lat']) ? floatval($_POST['user_lat']) : null;
    $user_lng = isset($_POST['user_lng']) ? floatval($_POST['user_lng']) : null;
    
    if (empty($search_term)) {
      wp_send_json_error(__('Search term is required.', 'bitcoiniacs-atm-locator'));
    }
    
    $filters = array('search' => $search_term);
    $locations = BitcoiniacsATMDatabase::get_all_locations($filters);
    
    // Calculate distances if user location is provided
    $formatted_locations = array();
    foreach ($locations as $location) {
      $location_data = array(
        'id' => intval($location->id),
        'name' => $location->location_name,
        'address' => $location->address,
        'province' => $location->province,
        'country' => $location->country,
        'latitude' => floatval($location->latitude),
        'longitude' => floatval($location->longitude),
        'status' => $location->status,
        'can_buy' => (bool) $location->can_buy,
        'can_sell' => (bool) $location->can_sell,
        'operating_hours' => $location->operating_hours,
        'phone' => $location->phone,
        'website' => $location->website,
        'notes' => $location->notes
      );
      
      // Calculate distance if user location is available
      if ($user_lat !== null && $user_lng !== null) {
        $distance = $this->calculate_distance(
          $user_lat, $user_lng,
          $location_data['latitude'], $location_data['longitude']
        );
        $location_data['distance'] = round($distance, 1);
      }
      
      $formatted_locations[] = $location_data;
    }
    
    // Sort by distance if available
    if ($user_lat !== null && $user_lng !== null) {
      usort($formatted_locations, function($a, $b) {
        return $a['distance'] <=> $b['distance'];
      });
    }
    
    wp_send_json_success($formatted_locations);
  }
  
  /**
   * Calculate distance between two points using Haversine formula
   */
  private function calculate_distance($lat1, $lng1, $lat2, $lng2) {
    $earth_radius = 6371; // Earth's radius in kilometers
    
    $lat1_rad = deg2rad($lat1);
    $lng1_rad = deg2rad($lng1);
    $lat2_rad = deg2rad($lat2);
    $lng2_rad = deg2rad($lng2);
    
    $delta_lat = $lat2_rad - $lat1_rad;
    $delta_lng = $lng2_rad - $lng1_rad;
    
    $a = sin($delta_lat / 2) * sin($delta_lat / 2) +
         cos($lat1_rad) * cos($lat2_rad) *
         sin($delta_lng / 2) * sin($delta_lng / 2);
    
    $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
    
    return $earth_radius * $c;
  }
}
