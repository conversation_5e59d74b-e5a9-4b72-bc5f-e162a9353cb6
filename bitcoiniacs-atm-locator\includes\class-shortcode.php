<?php
/**
 * Shortcode functionality for Bitcoiniacs ATM Locator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

class BitcoiniacsATMShortcode {
  
  /**
   * Constructor
   */
  public function __construct() {
    add_shortcode('bitcoiniacs_atm_locator', array($this, 'render_shortcode'));
    
    // Elementor compatibility
    add_action('elementor/widgets/widgets_registered', array($this, 'register_elementor_widget'));
  }
  
  /**
   * Render the shortcode
   */
  public function render_shortcode($atts) {
    $atts = shortcode_atts(array(
      'height' => '600',
      'zoom' => get_option('bitcoiniacs_atm_default_zoom', 4),
      'center_lat' => get_option('bitcoiniacs_atm_default_center_lat', 53.7267),
      'center_lng' => get_option('bitcoiniacs_atm_default_center_lng', -127.6476),
      'province' => '',
      'country' => '',
      'service' => '',
      'show_filters' => 'true',
      'show_search' => 'true',
      'show_locate_btn' => 'true',
      'show_visual_selector' => 'true',
      'map_style' => get_option('bitcoiniacs_atm_map_style', 'dark')
    ), $atts, 'bitcoiniacs_atm_locator');
    
    // Generate unique ID for this instance
    $map_id = 'bitcoiniacs-atm-map-' . uniqid();
    
    ob_start();
    ?>
    <div class="bitcoiniacs-atm-locator-container" id="<?php echo esc_attr($map_id); ?>-container">

      <?php if ($atts['show_visual_selector'] === 'true'): ?>
        <?php
        // Include visual selector template
        include BITCOINIACS_ATM_PLUGIN_PATH . 'templates/visual-selector.php';
        ?>
      <?php endif; ?>
      <?php if ($atts['show_filters'] === 'true'): ?>
        <div class="bitcoiniacs-atm-controls">
          <div class="bitcoiniacs-atm-control-group">
            <label for="<?php echo esc_attr($map_id); ?>-province-filter">
              <?php _e('Filter by Province', 'bitcoiniacs-atm-locator'); ?>
            </label>
            <select id="<?php echo esc_attr($map_id); ?>-province-filter" class="bitcoiniacs-province-filter">
              <option value="all"><?php _e('All Provinces', 'bitcoiniacs-atm-locator'); ?></option>
              <?php
              $provinces = BitcoiniacsATMDatabase::get_provinces();
              foreach ($provinces as $province) {
                $selected = ($atts['province'] === $province->province) ? 'selected' : '';
                echo '<option value="' . esc_attr($province->province) . '" ' . $selected . '>' . 
                     esc_html($province->province) . ' (' . esc_html($province->country) . ')</option>';
              }
              ?>
            </select>
          </div>
          
          <div class="bitcoiniacs-atm-control-group">
            <label for="<?php echo esc_attr($map_id); ?>-service-filter">
              <?php _e('Service Type', 'bitcoiniacs-atm-locator'); ?>
            </label>
            <select id="<?php echo esc_attr($map_id); ?>-service-filter" class="bitcoiniacs-service-filter">
              <option value="all"><?php _e('All Services', 'bitcoiniacs-atm-locator'); ?></option>
              <option value="buy" <?php selected($atts['service'], 'buy'); ?>><?php _e('Buy Only', 'bitcoiniacs-atm-locator'); ?></option>
              <option value="sell" <?php selected($atts['service'], 'sell'); ?>><?php _e('Sell Only', 'bitcoiniacs-atm-locator'); ?></option>
              <option value="both" <?php selected($atts['service'], 'both'); ?>><?php _e('Buy & Sell', 'bitcoiniacs-atm-locator'); ?></option>
            </select>
          </div>
          
          <?php if ($atts['show_search'] === 'true'): ?>
            <div class="bitcoiniacs-atm-control-group">
              <label for="<?php echo esc_attr($map_id); ?>-search-input">
                <?php _e('Search City/Address', 'bitcoiniacs-atm-locator'); ?>
              </label>
              <input type="text" id="<?php echo esc_attr($map_id); ?>-search-input" 
                     class="bitcoiniacs-search-input" 
                     placeholder="<?php _e('Enter city or address...', 'bitcoiniacs-atm-locator'); ?>">
            </div>
          <?php endif; ?>
          
          <?php if ($atts['show_locate_btn'] === 'true'): ?>
            <button class="bitcoiniacs-btn-locate" id="<?php echo esc_attr($map_id); ?>-locate-btn">
              📍 <?php _e('Find Nearest ATMs', 'bitcoiniacs-atm-locator'); ?>
            </button>
          <?php endif; ?>
          
          <button class="bitcoiniacs-btn-reset" id="<?php echo esc_attr($map_id); ?>-reset-btn">
            🔄 <?php _e('Reset View', 'bitcoiniacs-atm-locator'); ?>
          </button>
        </div>
      <?php endif; ?>
      
      <div class="bitcoiniacs-atm-map-container">
        <div id="<?php echo esc_attr($map_id); ?>" class="bitcoiniacs-atm-map" 
             style="height: <?php echo esc_attr($atts['height']); ?>px;"></div>
      </div>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
      // Initialize map for this instance
      BitcoiniacsATMLocator.initMap('<?php echo esc_js($map_id); ?>', {
        zoom: <?php echo intval($atts['zoom']); ?>,
        center: [<?php echo floatval($atts['center_lat']); ?>, <?php echo floatval($atts['center_lng']); ?>],
        province: '<?php echo esc_js($atts['province']); ?>',
        country: '<?php echo esc_js($atts['country']); ?>',
        service: '<?php echo esc_js($atts['service']); ?>',
        mapStyle: '<?php echo esc_js($atts['map_style']); ?>'
      });
    });
    </script>
    <?php
    
    return ob_get_clean();
  }
  
  /**
   * Register Elementor widget
   */
  public function register_elementor_widget() {
    if (class_exists('\Elementor\Plugin')) {
      require_once BITCOINIACS_ATM_PLUGIN_PATH . 'includes/class-elementor-widget.php';
      \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new BitcoiniacsATMElementorWidget());
    }
  }
}
