/**
 * Admin styles for Bitcoiniacs ATM Locator
 */

/* Admin Page Header */
.bitcoiniacs-atm-admin-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.bitcoiniacs-atm-admin-header h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 300;
}

.bitcoiniacs-atm-admin-header p {
  margin: 10px 0 0 0;
  opacity: 0.9;
}

/* Status Indicators */
.status-active {
  color: #46b450;
  font-weight: bold;
}

.status-inactive {
  color: #dc3232;
  font-weight: bold;
}

.status-maintenance {
  color: #ffb900;
  font-weight: bold;
}

/* Service Icons */
.service-icons .dashicons {
  color: #46b450;
  margin-right: 5px;
}

.service-icons .dashicons-yes-alt {
  color: #46b450;
}

.service-icons .dashicons-money-alt {
  color: #00a0d2;
}

/* Location Form Styles */
.bitcoiniacs-location-form {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.bitcoiniacs-location-form .form-table th {
  width: 200px;
  padding: 15px 10px 15px 0;
  vertical-align: top;
}

.bitcoiniacs-location-form .form-table td {
  padding: 15px 10px;
}

.bitcoiniacs-location-form .regular-text {
  width: 25em;
}

.bitcoiniacs-location-form .large-text {
  width: 99%;
}

/* Geocoding Button */
.geocode-button {
  margin-left: 10px;
  vertical-align: top;
}

.geocode-loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Location List Table */
.bitcoiniacs-locations-table {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
}

.bitcoiniacs-locations-table .wp-list-table {
  border: none;
}

.bitcoiniacs-locations-table .wp-list-table th,
.bitcoiniacs-locations-table .wp-list-table td {
  border-bottom: 1px solid #f0f0f1;
}

.bitcoiniacs-locations-table .wp-list-table tr:hover {
  background-color: #f6f7f7;
}

/* Action Buttons */
.bitcoiniacs-action-buttons {
  display: flex;
  gap: 5px;
  align-items: center;
}

.bitcoiniacs-action-buttons .button {
  margin: 0;
}

.button-delete {
  color: #d63638;
  border-color: #d63638;
}

.button-delete:hover {
  background: #d63638;
  color: #fff;
}

/* Settings Page */
.bitcoiniacs-settings-section {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.bitcoiniacs-settings-section h2 {
  margin-top: 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f1;
}

.bitcoiniacs-shortcode-example {
  background: #f6f7f7;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  margin: 15px 0;
  font-family: monospace;
  font-size: 14px;
}

.bitcoiniacs-shortcode-example code {
  background: none;
  padding: 0;
  color: #d63638;
  font-weight: bold;
}

/* Statistics Cards */
.bitcoiniacs-stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.bitcoiniacs-stat-card {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.bitcoiniacs-stat-card .stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #2271b1;
  margin-bottom: 5px;
  display: block;
}

.bitcoiniacs-stat-card .stat-label {
  color: #646970;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Bulk Actions */
.bitcoiniacs-bulk-actions {
  background: #f6f7f7;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px 15px;
  margin-bottom: 20px;
}

.bitcoiniacs-bulk-actions select {
  margin-right: 10px;
}

/* Loading States */
.bitcoiniacs-admin-loading {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #2271b1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Success/Error Messages */
.bitcoiniacs-admin-notice {
  padding: 12px;
  margin: 15px 0;
  border-radius: 4px;
  border-left: 4px solid;
}

.bitcoiniacs-admin-notice.notice-success {
  background: #f0f8f0;
  border-left-color: #46b450;
  color: #155724;
}

.bitcoiniacs-admin-notice.notice-error {
  background: #fdf2f2;
  border-left-color: #dc3232;
  color: #721c24;
}

.bitcoiniacs-admin-notice.notice-warning {
  background: #fff8e1;
  border-left-color: #ffb900;
  color: #8a6d3b;
}

/* Form Validation */
.bitcoiniacs-form-error {
  border-color: #dc3232 !important;
  box-shadow: 0 0 0 1px #dc3232 !important;
}

.bitcoiniacs-error-message {
  color: #dc3232;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

/* Responsive Admin */
@media (max-width: 782px) {
  .bitcoiniacs-stats-cards {
    grid-template-columns: 1fr;
  }
  
  .bitcoiniacs-action-buttons {
    flex-direction: column;
    align-items: stretch;
  }
  
  .bitcoiniacs-location-form .regular-text {
    width: 100%;
  }
}

/* Map Preview in Admin */
.bitcoiniacs-admin-map-preview {
  height: 300px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-top: 10px;
}

/* Import/Export Section */
.bitcoiniacs-import-export {
  background: #fff;
  border: 1px solid #ccd0d4;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.bitcoiniacs-import-export .button-primary {
  margin-right: 10px;
}

.bitcoiniacs-file-input {
  margin: 10px 0;
}

/* Help Text */
.bitcoiniacs-help-text {
  background: #f0f6fc;
  border: 1px solid #c3d9ff;
  border-radius: 4px;
  padding: 15px;
  margin: 15px 0;
  color: #0c5460;
}

.bitcoiniacs-help-text h4 {
  margin-top: 0;
  color: #0c5460;
}

.bitcoiniacs-help-text ul {
  margin-bottom: 0;
}

/* Plugin Info Box */
.bitcoiniacs-plugin-info {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.bitcoiniacs-plugin-info h3 {
  margin-top: 0;
  color: white;
}

.bitcoiniacs-plugin-info a {
  color: #fff;
  text-decoration: underline;
}

.bitcoiniacs-plugin-info a:hover {
  color: #f0f0f0;
}
