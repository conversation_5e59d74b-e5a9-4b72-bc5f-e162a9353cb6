<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoiniacs ATM Locator - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .test-header p {
            color: #666;
            font-size: 1.1rem;
        }
        .shortcode-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007cba;
        }
        .feature-item h3 {
            margin-top: 0;
            color: #333;
        }
        .installation-steps {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .installation-steps h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .installation-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .installation-steps li {
            margin-bottom: 10px;
        }
        .demo-placeholder {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 20px;
            text-align: center;
            border-radius: 10px;
            margin: 30px 0;
        }
        .demo-placeholder h2 {
            margin: 0 0 15px 0;
            font-size: 2rem;
        }
        .demo-placeholder p {
            margin: 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }
        .api-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 20px 0;
        }
        .api-example .comment {
            color: #68d391;
        }
        .api-example .string {
            color: #fbb6ce;
        }
        .api-example .keyword {
            color: #90cdf4;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🏧 Bitcoiniacs ATM Locator</h1>
            <p>WordPress Plugin Test & Documentation Page</p>
        </div>

        <div class="installation-steps">
            <h3>Quick Installation</h3>
            <ol>
                <li>Upload the plugin folder to <code>/wp-content/plugins/</code></li>
                <li>Activate the plugin in WordPress admin</li>
                <li>Add the shortcode to any page: <code>[bitcoiniacs_atm_locator]</code></li>
                <li>Or use the Elementor widget for page builders</li>
            </ol>
        </div>

        <h2>Plugin Features</h2>
        <div class="feature-list">
            <div class="feature-item">
                <h3>🗺️ Interactive Map</h3>
                <p>Powered by Leaflet with multiple map styles and responsive design</p>
            </div>
            <div class="feature-item">
                <h3>👁️ Visual Selector</h3>
                <p>Hierarchical country → region → city selection interface</p>
            </div>
            <div class="feature-item">
                <h3>⚙️ Admin Management</h3>
                <p>Full CRUD operations for ATM locations with geocoding support</p>
            </div>
            <div class="feature-item">
                <h3>🎨 Elementor Widget</h3>
                <p>Native integration with Elementor page builder</p>
            </div>
            <div class="feature-item">
                <h3>📍 Geolocation</h3>
                <p>Find nearest ATMs using user's current location</p>
            </div>
            <div class="feature-item">
                <h3>🔍 Advanced Filtering</h3>
                <p>Filter by province, service type, and search functionality</p>
            </div>
            <div class="feature-item">
                <h3>📱 Responsive Design</h3>
                <p>Mobile-friendly interface that works on all devices</p>
            </div>
            <div class="feature-item">
                <h3>🔌 REST API</h3>
                <p>RESTful endpoints for external integrations</p>
            </div>
        </div>

        <h2>Shortcode Examples</h2>
        
        <h3>Basic Usage</h3>
        <div class="shortcode-example">
[bitcoiniacs_atm_locator]
        </div>

        <h3>Customized Map</h3>
        <div class="shortcode-example">
[bitcoiniacs_atm_locator height="500" zoom="6" province="BC" map_style="light"]
        </div>

        <h3>Minimal Interface</h3>
        <div class="shortcode-example">
[bitcoiniacs_atm_locator show_visual_selector="false" show_filters="false" height="400"]
        </div>

        <div class="demo-placeholder">
            <h2>🗺️ Interactive Map Demo</h2>
            <p>The ATM locator map would appear here when the plugin is active</p>
            <p>Features: Visual selector, filters, geolocation, and responsive design</p>
        </div>

        <h2>REST API Examples</h2>
        
        <h3>Get All Locations</h3>
        <div class="api-example">
<span class="comment">// Get all ATM locations</span>
<span class="keyword">GET</span> <span class="string">/wp-json/bitcoiniacs-atm/v1/locations</span>

<span class="comment">// Filter by province</span>
<span class="keyword">GET</span> <span class="string">/wp-json/bitcoiniacs-atm/v1/locations?province=BC</span>

<span class="comment">// Search near user location</span>
<span class="keyword">GET</span> <span class="string">/wp-json/bitcoiniacs-atm/v1/locations?lat=49.2827&lng=-123.1207&radius=10</span>
        </div>

        <h3>Get Single Location</h3>
        <div class="api-example">
<span class="comment">// Get specific ATM location</span>
<span class="keyword">GET</span> <span class="string">/wp-json/bitcoiniacs-atm/v1/locations/1</span>
        </div>

        <h2>Database Schema</h2>
        <p>The plugin creates a custom table <code>wp_bitcoiniacs_atm_locations</code> with the following structure:</p>
        
        <div class="api-example">
<span class="comment">-- ATM Locations Table Structure</span>
<span class="keyword">CREATE TABLE</span> wp_bitcoiniacs_atm_locations (
  id <span class="keyword">mediumint</span>(<span class="string">9</span>) <span class="keyword">NOT NULL AUTO_INCREMENT</span>,
  location_name <span class="keyword">varchar</span>(<span class="string">255</span>) <span class="keyword">NOT NULL</span>,
  address <span class="keyword">text NOT NULL</span>,
  province <span class="keyword">varchar</span>(<span class="string">10</span>) <span class="keyword">NOT NULL</span>,
  country <span class="keyword">varchar</span>(<span class="string">10</span>) <span class="keyword">DEFAULT</span> <span class="string">'CA'</span>,
  latitude <span class="keyword">decimal</span>(<span class="string">10, 8</span>) <span class="keyword">NOT NULL</span>,
  longitude <span class="keyword">decimal</span>(<span class="string">11, 8</span>) <span class="keyword">NOT NULL</span>,
  status <span class="keyword">varchar</span>(<span class="string">20</span>) <span class="keyword">DEFAULT</span> <span class="string">'Active'</span>,
  can_buy <span class="keyword">tinyint</span>(<span class="string">1</span>) <span class="keyword">DEFAULT</span> <span class="string">1</span>,
  can_sell <span class="keyword">tinyint</span>(<span class="string">1</span>) <span class="keyword">DEFAULT</span> <span class="string">0</span>,
  operating_hours <span class="keyword">text</span>,
  phone <span class="keyword">varchar</span>(<span class="string">20</span>),
  website <span class="keyword">varchar</span>(<span class="string">255</span>),
  notes <span class="keyword">text</span>,
  created_at <span class="keyword">datetime DEFAULT CURRENT_TIMESTAMP</span>,
  updated_at <span class="keyword">datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
  <span class="keyword">PRIMARY KEY</span> (id)
);
        </div>

        <h2>Requirements</h2>
        <ul>
            <li><strong>WordPress:</strong> 5.0 or higher</li>
            <li><strong>PHP:</strong> 7.4 or higher</li>
            <li><strong>MySQL:</strong> 5.6 or higher</li>
            <li><strong>Browser:</strong> Modern browsers with JavaScript enabled</li>
            <li><strong>HTTPS:</strong> Required for geolocation features</li>
        </ul>

        <h2>Seed Data</h2>
        <p>The plugin comes pre-populated with ATM locations across:</p>
        <ul>
            <li><strong>Canada:</strong> Alberta, British Columbia, Ontario</li>
            <li><strong>Philippines:</strong> Metro Manila</li>
        </ul>
        <p>All seed data includes real addresses, coordinates, and service information.</p>

        <div class="installation-steps">
            <h3>Next Steps After Installation</h3>
            <ol>
                <li>Visit <strong>ATM Locations → Settings</strong> to configure defaults</li>
                <li>Add your own ATM locations via <strong>ATM Locations → Add New</strong></li>
                <li>Test the shortcode on a page: <code>[bitcoiniacs_atm_locator]</code></li>
                <li>Customize styling to match your theme</li>
                <li>Set up Elementor widget if using Elementor</li>
            </ol>
        </div>
    </div>
</body>
</html>
