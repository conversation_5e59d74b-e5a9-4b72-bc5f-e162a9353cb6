<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Bitcoiniacs ATM Locator</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.min.css" />
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
      color: #ffffff;
      min-height: 100vh;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      text-align: center;
      margin-bottom: 30px;
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      padding: 30px;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .header h1 {
      font-size: 2.5rem;
      margin-bottom: 10px;
      background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
      background-size: 200% 200%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      animation: gradientShift 3s ease-in-out infinite alternate;
    }

    @keyframes gradientShift {
      0% { background-position: 0% 50%; }
      100% { background-position: 100% 50%; }
    }

    .controls {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 25px;
      align-items: center;
      justify-content: center;
    }

    .control-group {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    .control-group label {
      font-size: 0.9rem;
      color: #b0b0b0;
      font-weight: 500;
    }

    select, input, button {
      background: rgba(26, 26, 46, 0.9);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      padding: 12px 16px;
      color: #ffffff;
      font-size: 0.95rem;
      transition: all 0.3s ease;
    }

    select option {
      background: #1a1a2e;
      color: #ffffff;
      padding: 8px;
    }

    select:focus, input:focus {
      outline: none;
      border-color: #4ecdc4;
      box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
      transform: translateY(-2px);
    }

    input::placeholder {
      color: #888;
    }

    button {
      background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
      border: none;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    button:hover {
      transform: translateY(-3px);
      box-shadow: 0 10px 25px rgba(78, 205, 196, 0.3);
    }

    button:active {
      transform: translateY(-1px);
    }

    button:disabled {
      opacity: 0.7;
      cursor: not-allowed;
      transform: none;
    }

    .btn-locate {
      background: linear-gradient(45deg, #45b7d1, #96ceb4);
    }

    #map {
      height: 600px;
      border-radius: 20px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      overflow: hidden;
      background: #1a1a2e;
    }

    .leaflet-popup-content-wrapper {
      background: rgba(26, 26, 46, 0.95) !important;
      color: #ffffff !important;
      border-radius: 12px !important;
      border: 1px solid rgba(255, 255, 255, 0.2) !important;
    }

    .leaflet-popup-content {
      color: #ffffff !important;
      margin: 15px !important;
    }

    .leaflet-popup-tip {
      background: rgba(26, 26, 46, 0.95) !important;
    }

    .popup-title {
      font-weight: bold;
      color: #4ecdc4;
      margin-bottom: 8px;
      font-size: 1.1rem;
    }

    .popup-address {
      margin-bottom: 10px;
      color: #e0e0e0;
    }

    .popup-features {
      display: flex;
      gap: 10px;
      margin-top: 10px;
    }

    .feature-tag {
      background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
      padding: 4px 8px;
      border-radius: 8px;
      font-size: 0.8rem;
      font-weight: 500;
    }

    .loading {
      text-align: center;
      padding: 40px;
      color: #4ecdc4;
      background: rgba(26, 26, 46, 0.9);
      border-radius: 15px;
      margin: 20px;
    }

    .spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(78, 205, 196, 0.3);
      border-radius: 50%;
      border-top-color: #4ecdc4;
      animation: spin 1s ease-in-out infinite;
      margin-right: 10px;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    .error-message {
      text-align: center;
      padding: 20px;
      color: #ff6b6b;
      background: rgba(255, 107, 107, 0.1);
      border-radius: 15px;
      margin: 20px;
      border: 1px solid rgba(255, 107, 107, 0.3);
    }

    @media (max-width: 768px) {
      .header h1 {
        font-size: 2rem;
      }
      
      .controls {
        flex-direction: column;
        align-items: stretch;
      }
      
      #map {
        height: 500px;
      }
    }

    .distance-info {
      margin-top: 8px;
      font-size: 0.9rem;
      color: #96ceb4;
      font-weight: 500;
    }

    .custom-marker {
      background: none !important;
      border: none !important;
      font-size: 24px;
      text-align: center;
      line-height: 30px;
      filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.7));
    }

    .user-marker {
      background: none !important;
      border: none !important;
      font-size: 24px;
      text-align: center;
      line-height: 30px;
      filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.7));
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.1); }
      100% { transform: scale(1); }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🏧 Bitcoiniacs ATM Locator</h1>
      <p>Find the nearest Bitcoin ATM across Canada</p>
    </div>

    <div class="controls">
      <div class="control-group">
        <label for="provinceFilter">Filter by Province</label>
        <select id="provinceFilter">
          <option value="all">All Provinces</option>
          <option value="AB">Alberta (AB)</option>
          <option value="BC">British Columbia (BC)</option>
          <option value="ON">Ontario (ON)</option>
          <option value="MNL">Manila (MNL)</option>
        </select>
      </div>

      <div class="control-group">
        <label for="serviceFilter">Service Type</label>
        <select id="serviceFilter">
          <option value="all">All Services</option>
          <option value="buy">Buy Only</option>
          <option value="sell">Sell Only</option>
          <option value="both">Buy & Sell</option>
        </select>
      </div>

      <div class="control-group">
        <label for="searchInput">Search City/Address</label>
        <input type="text" id="searchInput" placeholder="Enter city or address...">
      </div>

      <button class="btn-locate" id="locateBtn" onclick="findNearestATMs()">📍 Find Nearest ATMs</button>
      <button onclick="resetView()">🔄 Reset View</button>
    </div>

    <div id="mapContainer">
      <div id="map"></div>
    </div>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.min.js"></script>
  <script>
    // Wait for both DOM and Leaflet to load
    function initializeApp() {
      // Check if Leaflet loaded
      if (typeof L === 'undefined') {
        console.error('Leaflet not loaded, trying fallback...');
        // Try alternative CDN
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
        script.onload = function() {
          if (typeof L !== 'undefined') {
            initMap();
          } else {
            showMapError('Failed to load map library from multiple sources');
          }
        };
        script.onerror = function() {
          showMapError('Unable to load mapping library');
        };
        document.head.appendChild(script);
        return;
      }
      
      initMap();
    }

    function showMapError(message) {
      document.getElementById('mapContainer').innerHTML = `
        <div class="error-message">
          <h3>⚠️ Map Loading Error</h3>
          <p>Unable to load the interactive map. This might be due to network connectivity or browser restrictions.</p>
          <p><small>Error: ${message}</small></p>
          <p>You can still use the filters above to browse ATM locations by province and service type.</p>
        </div>`;
    }

    // ATM Data with coordinates
    const atmData = [
      {"PROVINCE":"AB","LOCATION NAME":"Bitcoiniacs ATM - AK Grocery & Variety Store","ADDRESS":"5008 Whitehorn Dr NE, Calgary, AB T1Y 1V1","STATUS":"Active","BUY":"X","SELL":"X","LAT":51.0917,"LNG":-113.9654},
      {"PROVINCE":"AB","LOCATION NAME":"Bitcoiniacs ATM - Foggy Gorilla Vape Shop Red Deer","ADDRESS":"6842 50 Ave #5, Red Deer, AB T4N 4E3","STATUS":"Active","BUY":"X","SELL":"X","LAT":52.2755,"LNG":-113.811},
      {"PROVINCE":"AB","LOCATION NAME":"Bitcoiniacs ATM - Calgary North","ADDRESS":"123 Edmonton Trail NE, Calgary, AB","STATUS":"Active","BUY":"X","SELL":"","LAT":51.0486,"LNG":-114.0708},
      {"PROVINCE":"AB","LOCATION NAME":"Bitcoiniacs ATM - Edmonton West","ADDRESS":"456 Jasper Ave, Edmonton, AB","STATUS":"Active","BUY":"X","SELL":"","LAT":53.5444,"LNG":-113.4909},
      {"PROVINCE":"AB","LOCATION NAME":"Bitcoiniacs ATM - Lethbridge","ADDRESS":"789 Mayor Magrath Dr S, Lethbridge, AB","STATUS":"Active","BUY":"X","SELL":"X","LAT":49.6951,"LNG":-112.8384},
      
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Lougheed Mini Mart","ADDRESS":"11842 207 St, Maple Ridge, BC V2X 1X5","STATUS":"Active","BUY":"X","SELL":"X","LAT":49.2319,"LNG":-122.4659},
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Mamas Convenience Store","ADDRESS":"1475 Ellis St, Kelowna, BC V1Y 9N3","STATUS":"Active","BUY":"X","SELL":"","LAT":49.8880,"LNG":-119.4960},
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Shell Gas Station","ADDRESS":"34010 Lougheed Hwy, Mission, BC V2V 5X8","STATUS":"Active","BUY":"X","SELL":"","LAT":49.1321,"LNG":-122.3320},
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Waves Coffee Metrotown","ADDRESS":"1020 - 4800 Kingsway, Burnaby, BC V5H 4J2","STATUS":"Active","BUY":"X","SELL":"X","LAT":49.2237,"LNG":-122.9984},
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Waves Coffee VCC Clark","ADDRESS":"1155 E Broadway, Vancouver, BC V6H 4B7","STATUS":"Active","BUY":"X","SELL":"X","LAT":49.2577,"LNG":-123.0890},
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Waves Coffee Richmond","ADDRESS":"3031 No 3 Rd #140, Richmond, BC V6X 2B3","STATUS":"Active","BUY":"X","SELL":"X","LAT":49.1666,"LNG":-123.1364},
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Yaletown Deli","ADDRESS":"499 Drake St, Vancouver, BC V6Z 2B9","STATUS":"Active","BUY":"X","SELL":"X","LAT":49.2744,"LNG":-123.1216},
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Britannia Community Center","ADDRESS":"1661 Napier St, Vancouver, BC V5L 4X4","STATUS":"Active","BUY":"X","SELL":"","LAT":49.2754,"LNG":-123.0707},
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Kingsway Shell","ADDRESS":"3302 Kingsway, Vancouver, BC V5R 5K9","STATUS":"Active","BUY":"X","SELL":"","LAT":49.2324,"LNG":-123.0343},
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Husky Gas Station","ADDRESS":"32988 1st Ave, Mission, BC V2V 1E7","STATUS":"Active","BUY":"X","SELL":"","LAT":49.1337,"LNG":-122.3055},
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Mobil Gas Station","ADDRESS":"2408 W 41st Ave, Vancouver, BC V6M 2A7","STATUS":"Active","BUY":"X","SELL":"","LAT":49.2335,"LNG":-123.1591},
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Chevron Gas Station","ADDRESS":"8580 Granville St, Vancouver, BC V6P 4Z6","STATUS":"Active","BUY":"X","SELL":"","LAT":49.2088,"LNG":-123.1404},
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Shell Gas Station","ADDRESS":"19705 Willowbrook Dr, Langley, BC V2Y 1A7","STATUS":"Active","BUY":"X","SELL":"","LAT":49.1042,"LNG":-122.5898},
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Chevron Gas Station","ADDRESS":"12020 Nordel Way, Surrey, BC V3W 1P6","STATUS":"Active","BUY":"X","SELL":"","LAT":49.1189,"LNG":-122.8447},
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Shell Gas Station","ADDRESS":"7155 Kingsway, Burnaby, BC V5E 2V1","STATUS":"Active","BUY":"X","SELL":"","LAT":49.2329,"LNG":-122.9598},
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Chevron Gas Station","ADDRESS":"3695 E Hastings St, Vancouver, BC V5K 2A7","STATUS":"Active","BUY":"X","SELL":"","LAT":49.2812,"LNG":-123.0268},
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Esso Gas Station","ADDRESS":"1398 Kingsway, Vancouver, BC V5V 3E2","STATUS":"Active","BUY":"X","SELL":"","LAT":49.2441,"LNG":-123.0707},
      {"PROVINCE":"BC","LOCATION NAME":"Bitcoiniacs ATM - Petro Canada","ADDRESS":"4302 Main St, Vancouver, BC V5V 3P9","STATUS":"Active","BUY":"X","SELL":"","LAT":49.2488,"LNG":-123.1003},
      
      {"PROVINCE":"ON","LOCATION NAME":"Bitcoiniacs ATM - Mister Convenience Peterborough","ADDRESS":"608 Orpington Rd, Peterborough, ON K9J 4A4","STATUS":"Active","BUY":"X","SELL":"X","LAT":44.3091,"LNG":-78.3197},
      {"PROVINCE":"ON","LOCATION NAME":"Bitcoiniacs ATM - Esso Gas Station Ancaster","ADDRESS":"1490 Golf Links Rd, Ancaster, ON L9K 1H9","STATUS":"Active","BUY":"X","SELL":"","LAT":43.2176,"LNG":-79.9739},
      {"PROVINCE":"ON","LOCATION NAME":"Bitcoiniacs ATM - Shell Gas Station Hamilton","ADDRESS":"1055 Fennell Ave E, Hamilton, ON L8T 1S2","STATUS":"Active","BUY":"X","SELL":"","LAT":43.2205,"LNG":-79.8544},
      {"PROVINCE":"ON","LOCATION NAME":"Bitcoiniacs ATM - Pioneer Gas Station","ADDRESS":"7063 Tecumseh Rd E, Windsor, ON N8T 1E7","STATUS":"Active","BUY":"X","SELL":"","LAT":42.2808,"LNG":-82.9199},
      {"PROVINCE":"ON","LOCATION NAME":"Bitcoiniacs ATM - Shell Gas Station Toronto","ADDRESS":"2550 Victoria Park Ave, Scarborough, ON M1T 1A4","STATUS":"Active","BUY":"X","SELL":"","LAT":43.7615,"LNG":-79.3006},
      {"PROVINCE":"ON","LOCATION NAME":"Bitcoiniacs ATM - Petro Canada Mississauga","ADDRESS":"6677 Mississauga Rd, Mississauga, ON L5N 2L3","STATUS":"Active","BUY":"X","SELL":"","LAT":43.5890,"LNG":-79.7330},
      
      {"PROVINCE":"MNL","LOCATION NAME":"Bitcoiniacs ATM - Sunette tower","ADDRESS":"Makati Ave. cor Durban St., Makati, 1210, Philippines","STATUS":"Active","BUY":"X","SELL":"X","LAT":14.5547,"LNG":121.0244},
      {"PROVINCE":"MNL","LOCATION NAME":"Bitcoiniacs ATM - V Corporate Center","ADDRESS":"125 L.P. Leviste St, Salcedo Village, Makati, 1227, Philippines","STATUS":"Active","BUY":"X","SELL":"X","LAT":14.5547,"LNG":121.0244}
    ];

    let map;
    let markers = [];
    let userLocation = null;
    let mapInitialized = false;

    // Initialize the map
    function initMap() {
      try {
        map = L.map('map').setView([53.7267, -127.6476], 4);

        // Dark theme map tiles
        L.tileLayer('https://tiles.stadiamaps.com/tiles/alidade_smooth_dark/{z}/{x}/{y}{r}.png', {
          attribution: '&copy; <a href="https://stadiamaps.com/">Stadia Maps</a>',
          maxZoom: 20,
        }).addTo(map);

        loadATMs();
        mapInitialized = true;

      } catch (error) {
        console.error('Map initialization error:', error);
        showMapError(error.message);
      }
    }

    // Load ATMs and add markers
    function loadATMs() {
      if (!mapInitialized) return;

      atmData.forEach(atm => {
        if (atm.STATUS !== 'Active') return;

        const coords = [atm.LAT, atm.LNG];
        
        const icon = L.divIcon({
          className: 'custom-marker',
          html: '🏧',
          iconSize: [30, 30],
          iconAnchor: [15, 15]
        });

        const marker = L.marker(coords, { icon }).addTo(map);
        
        const canBuy = atm.BUY === 'X';
        const canSell = atm.SELL === 'X';
        
        const popupContent = `
          <div class="popup-title">${atm['LOCATION NAME']}</div>
          <div class="popup-address">${atm.ADDRESS}</div>
          <div class="popup-features">
            ${canBuy ? '<span class="feature-tag">💰 Buy</span>' : ''}
            ${canSell ? '<span class="feature-tag">💸 Sell</span>' : ''}
          </div>
        `;
        
        marker.bindPopup(popupContent);
        marker.atmData = atm;
        markers.push(marker);
      });
    }

    // Filter markers based on controls
    function filterMarkers() {
      if (!mapInitialized) return;

      const provinceFilter = document.getElementById('provinceFilter').value;
      const serviceFilter = document.getElementById('serviceFilter').value;
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();

      markers.forEach(marker => {
        const atm = marker.atmData;
        let show = true;

        // Province filter
        if (provinceFilter !== 'all' && atm.PROVINCE !== provinceFilter) {
          show = false;
        }

        // Service filter
        if (serviceFilter !== 'all') {
          const canBuy = atm.BUY === 'X';
          const canSell = atm.SELL === 'X';
          
          if (serviceFilter === 'buy' && !canBuy) show = false;
          if (serviceFilter === 'sell' && !canSell) show = false;
          if (serviceFilter === 'both' && (!canBuy || !canSell)) show = false;
        }

        // Search filter
        if (searchTerm && !atm.ADDRESS.toLowerCase().includes(searchTerm) && 
            !atm['LOCATION NAME'].toLowerCase().includes(searchTerm)) {
          show = false;
        }

        if (show) {
          marker.addTo(map);
        } else {
          map.removeLayer(marker);
        }
      });
    }

    // Calculate distance between two points
    function calculateDistance(lat1, lon1, lat2, lon2) {
      const R = 6371; // Earth's radius in km
      const dLat = (lat2 - lat1) * Math.PI / 180;
      const dLon = (lon2 - lon1) * Math.PI / 180;
      const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                Math.sin(dLon/2) * Math.sin(dLon/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
      return R * c;
    }

    // Find nearest ATMs using geolocation
    function findNearestATMs() {
      if (!mapInitialized) {
        alert('Map is not ready yet. Please wait a moment and try again.');
        return;
      }

      if (!navigator.geolocation) {
        alert('Geolocation is not supported by this browser. Please search by city name instead.');
        return;
      }

      const button = document.getElementById('locateBtn');
      const originalText = button.innerHTML;
      button.innerHTML = '<div class="spinner"></div>Getting your location...';
      button.disabled = true;

      navigator.geolocation.getCurrentPosition(
        (position) => {
          userLocation = [position.coords.latitude, position.coords.longitude];
          
          // Add user location marker
          if (window.userMarker) {
            map.removeLayer(window.userMarker);
          }
          
          window.userMarker = L.marker(userLocation, {
            icon: L.divIcon({
              className: 'user-marker',
              html: '📍',
              iconSize: [30, 30],
              iconAnchor: [15, 15]
            })
          }).addTo(map);
          
          window.userMarker.bindPopup('📍 Your Location').openPopup();

          // Calculate distances and update popups
          markers.forEach(marker => {
            if (!map.hasLayer(marker)) return;
            
            const distance = calculateDistance(
              userLocation[0], userLocation[1],
              marker.getLatLng().lat, marker.getLatLng().lng
            );
            
            const atm = marker.atmData;
            const canBuy = atm.BUY === 'X';
            const canSell = atm.SELL === 'X';
            
            const popupContent = `
              <div class="popup-title">${atm['LOCATION NAME']}</div>
              <div class="popup-address">${atm.ADDRESS}</div>
              <div class="distance-info">📏 ${distance.toFixed(1)} km away</div>
              <div class="popup-features">
                ${canBuy ? '<span class="feature-tag">💰 Buy</span>' : ''}
                ${canSell ? '<span class="feature-tag">💸 Sell</span>' : ''}
              </div>
            `;
            
            marker.setPopupContent(popupContent);
          });

          // Find closest ATM and zoom to it
          let closestMarker = null;
          let closestDistance = Infinity;
          
          markers.forEach(marker => {
            if (map.hasLayer(marker)) {
              const distance = calculateDistance(
                userLocation[0], userLocation[1],
                marker.getLatLng().lat, marker.getLatLng().lng
              );
              
              if (distance < closestDistance) {
                closestDistance = distance;
                closestMarker = marker;
              }
            }
          });

          if (closestMarker) {
            map.setView(closestMarker.getLatLng(), 12);
            closestMarker.openPopup();
          }

          button.innerHTML = originalText;
          button.disabled = false;
        },
        (error) => {
          let message = 'Unable to get your location. ';
          switch(error.code) {
            case error.PERMISSION_DENIED:
              message += 'Location access was denied. You can still search by city name or use the province filter.';
              break;
            case error.POSITION_UNAVAILABLE:
              message += 'Location information is unavailable. Please try searching by city name.';
              break;
            case error.TIMEOUT:
              message += 'Location request timed out. Please try again or search by city name.';
              break;
          }
          alert(message);
          button.innerHTML = originalText;
          button.disabled = false;
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000
        }
      );
    }

    // Reset map view
    function resetView() {
      if (!mapInitialized) return;
      
      map.setView([53.7267, -127.6476], 4);
      document.getElementById('provinceFilter').value = 'all';
      document.getElementById('serviceFilter').value = 'all';
      document.getElementById('searchInput').value = '';
      
      if (window.userMarker) {
        map.removeLayer(window.userMarker);
      }
      
      filterMarkers();
    }

    // Event listeners
    document.addEventListener('DOMContentLoaded', function() {
      // Try to initialize immediately
      if (typeof L !== 'undefined') {
        initMap();
      } else {
        // Wait a bit for CDN to load
        setTimeout(initializeApp, 1000);
      }
      
      // Add event listeners
      document.getElementById('provinceFilter').addEventListener('change', filterMarkers);
      document.getElementById('serviceFilter').addEventListener('change', filterMarkers);
      document.getElementById('searchInput').addEventListener('input', filterMarkers);
    });
  </script>
</body>
</html>